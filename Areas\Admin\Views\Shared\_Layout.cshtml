<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] | CineZore Admin</title>
    <link rel="icon" href="~/images/imagescinezore-logo.png" type="image/png">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/admin.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            transform: translateX(5px);
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        
        .admin-header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 0;
        }
        
        .admin-content {
            background: #f8f9fa;
            min-height: calc(100vh - 80px);
            padding: 30px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-card p {
            margin: 0;
            opacity: 0.9;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="~/images/imagescinezore-logo.png" alt="CineZore" style="height: 40px;" />
                        <h5 class="text-white mt-2">Admin Panel</h5>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="Admin" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="Admin" asp-controller="Users" asp-action="Index">
                                <i class="fas fa-users"></i>
                                Quản lý người dùng
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="Admin" asp-controller="Movies" asp-action="Index">
                                <i class="fas fa-film"></i>
                                Quản lý phim
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="LichChieu" asp-action="Index">
                                <i class="fas fa-calendar-alt"></i>
                                Quản lý lịch chiếu
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="AdminBooking" asp-action="Index">
                                <i class="fas fa-ticket-alt"></i>
                                Quản lý đặt vé
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="AdminTicket" asp-action="Verify">
                                <i class="fas fa-qrcode"></i>
                                Kiểm tra vé
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <hr class="text-white-50">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home"></i>
                                Về trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Account" asp-action="Logout">
                                <i class="fas fa-sign-out-alt"></i>
                                Đăng xuất
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="admin-header">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center">
                        <h1 class="h2">@ViewData["Title"]</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <span class="text-muted">
                                    <i class="fas fa-user-shield me-1"></i>
                                    Xin chào, @User.Identity.Name
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="admin-content">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    @if (TempData["InfoMessage"] != null)
                    {
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            @TempData["InfoMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    @RenderBody()
                </div>
            </main>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <script>
        $(document).ready(function() {
            // Highlight active menu item
            var currentPath = window.location.pathname.toLowerCase();
            $('.admin-sidebar .nav-link').each(function() {
                var linkPath = $(this).attr('href');
                if (linkPath && currentPath.indexOf(linkPath.toLowerCase()) !== -1) {
                    $(this).addClass('active');
                }
            });
        });
    </script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
