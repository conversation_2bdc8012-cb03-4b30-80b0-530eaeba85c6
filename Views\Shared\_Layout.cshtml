﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' https: blob:; img-src 'self' https: data:; style-src 'self' https: 'unsafe-inline'; script-src 'self' https: 'unsafe-inline'; connect-src 'self' https:; media-src 'self' https: data:; object-src 'none'; frame-src 'self' https:; worker-src 'self' blob:; child-src 'self' blob:">
    <meta http-equiv="Permissions-Policy" content="camera=*, microphone=*">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <title>@ViewData["Title"] | CineZore</title>
    <link rel="icon" href="~/images/imagescinezore-logo.png" type="image/png">
    <link rel="shortcut icon" href="~/images/imagescinezore-logo.png" type="image/png">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/override.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/theme.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/effects.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/CinemaBooking.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .brand-logo {
            height: 40px;
            filter: brightness(1.1);
        }
        .feedback-icon.hide {
            display: none !important;
        }

        /* Hiệu ứng ripple cho các nút */
        .ripple {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        @@keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .btn-ripple {
            position: relative;
            overflow: hidden;
        }

        /* CSS cho form tìm kiếm */
        .search-form {
            min-width: 200px;
        }

        @@media (min-width: 768px) {
            .search-form {
                min-width: 300px;
            }
        }

        .search-input {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            background-color: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            background-color: #fff;
            box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
            border-color: rgba(229, 9, 20, 0.5);
        }

        .search-form .btn-danger {
            background-color: #e50914;
            border-color: #e50914;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .search-form .btn-danger:hover {
            background-color: #c00;
            border-color: #c00;
        }

        /* CSS cho thanh điều hướng mới */
        .main-navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* Thanh điều hướng phụ (đen) */
        .secondary-navbar {
            font-size: 0.85rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background-color: #121212 !important;
            min-height: 30px;
        }

        .secondary-navbar .nav-link {
            padding: 0.25rem 0.75rem;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
        }

        .secondary-navbar .nav-link:hover {
            color: #fff;
        }

        /* Thanh điều hướng chính (xám đen) */
        .navbar-dark.bg-dark {
            background-color: #212121 !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        /* Thanh điều hướng thứ ba (đỏ) */
        .tertiary-navbar {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background-color: #e50914 !important;
        }

        .tertiary-navbar .nav-link {
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            transition: all 0.2s ease;
            position: relative;
        }

        .tertiary-navbar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .tertiary-navbar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .tertiary-navbar .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 3px;
            background-color: #fff;
            border-radius: 3px 3px 0 0;
        }

        /* Logo và thương hiệu */
        .navbar-brand {
            padding: 0.5rem 0;
        }

        .logo-image {
            height: 35px;
            width: auto;
            margin-right: 0.5rem;
        }

        .brand-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: #e50914;
            letter-spacing: 0.5px;
        }

        /* Form tìm kiếm */
        .search-form {
            max-width: 500px;
            width: 100%;
        }

        .search-input {
            border-color: rgba(255, 255, 255, 0.2);
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .search-input:focus {
            background-color: rgba(255, 255, 255, 0.15);
            color: #fff;
            border-color: rgba(229, 9, 20, 0.5);
        }

        /* Responsive adjustments */
        @@media (max-width: 992px) {
            .tertiary-navbar .navbar-nav {
                flex-direction: row;
                overflow-x: auto;
                white-space: nowrap;
                -webkit-overflow-scrolling: touch;
                padding-bottom: 5px;
            }

            .tertiary-navbar .nav-item {
                display: inline-block;
            }

            .search-form {
                max-width: 300px;
            }
        }

        @@media (max-width: 768px) {
            .secondary-navbar {
                display: none;
            }

            .search-form {
                max-width: 200px;
            }
        }
    </style>
    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="@(ViewContext.RouteData.Values["Controller"].ToString() == "Home" && ViewContext.RouteData.Values["Action"].ToString() == "Index" ? "home-page" : "")">
    <header>
        <div class="main-navbar">
            <!-- Thanh điều hướng phụ -->
            <nav class="navbar navbar-expand navbar-dark bg-black py-0 secondary-navbar">
                <div class="container-fluid">
                    <ul class="navbar-nav me-auto small">
                        <li class="nav-item">
                            <a class="nav-link py-1" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home"></i> Trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-1" asp-controller="Search" asp-action="MoviesByDate">
                                <i class="fas fa-calendar-day"></i> Lịch chiếu hôm nay
                            </a>
                        </li>
                        @if (User.IsInRole("Admin"))
                        {
                            <li class="nav-item">
                                <a class="nav-link py-1" asp-area="Admin" asp-controller="Home" asp-action="Index">
                                    <i class="fas fa-tachometer-alt"></i> Admin Panel
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link py-1" asp-controller="Phim" asp-action="Index">
                                    <i class="fas fa-film"></i> Quản lý phim
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link py-1" asp-controller="LichChieu" asp-action="Index">
                                    <i class="fas fa-calendar-alt"></i> Quản lý lịch chiếu
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link py-1" asp-controller="AdminBooking" asp-action="Index">
                                    <i class="fas fa-ticket-alt"></i> Quản lý đặt vé
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link py-1" asp-controller="AdminTicket" asp-action="Verify">
                                    <i class="fas fa-qrcode"></i> Kiểm tra vé
                                </a>
                            </li>
                        }
                    </ul>
                    <ul class="navbar-nav ms-auto small">
                        <li class="nav-item">
                            <a class="nav-link py-1" href="#">
                                <i class="fas fa-info-circle"></i> Trợ giúp
                            </a>
                        </li>
                        @if (User.Identity.IsAuthenticated)
                        {
                            <li class="nav-item">
                                <span class="nav-link py-1">
                                    <i class="fas fa-user"></i> Xin chào, @User.FindFirst("FullName")?.Value!
                                </span>
                            </li>
                        }
                    </ul>
                </div>
            </nav>

            <!-- Thanh điều hướng chính -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark py-2">
                <div class="container-fluid">
                    <!-- Logo và thương hiệu -->
                    <a class="navbar-brand d-flex align-items-center" asp-controller="Home" asp-action="Index">
                        <img src="/images/imagescinezore-logo.png" alt="CineZore" class="logo-image" />
                        <span class="brand-text">CineZore</span>
                    </a>

                    <!-- Form tìm kiếm -->
                    <form class="d-flex mx-auto search-form" asp-controller="Search" asp-action="Index" method="get">
                        <div class="input-group">
                            <input type="text" name="searchTerm" class="form-control search-input"
                                   placeholder="Tìm kiếm phim..." aria-label="Tìm kiếm">
                            <button class="btn btn-danger" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>

                    <!-- Menu bên phải -->
                    <div class="d-flex align-items-center">
                        @if (User.Identity.IsAuthenticated)
                        {
                            <div class="dropdown">
                                <a class="btn btn-outline-light dropdown-toggle" href="#" role="button"
                                   id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle me-1"></i>
                                    <span class="d-none d-md-inline">Tài khoản</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li>
                                        <span class="dropdown-item-text">
                                            <i class="fas fa-user me-1"></i>Xin chào, @User.FindFirst("FullName")?.Value!
                                        </span>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" asp-controller="UserProfile" asp-action="Index">
                                            <i class="bi bi-person me-1"></i>Tài khoản
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" asp-controller="Home" asp-action="LichSuDatVe">
                                            <i class="fas fa-history me-1"></i>Lịch sử đặt vé
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" asp-controller="Account" asp-action="Logout">
                                            <i class="fas fa-sign-out-alt me-1"></i>Đăng xuất
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <a class="btn btn-outline-light me-2" asp-controller="Account" asp-action="Login">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                <span class="d-none d-md-inline">Đăng nhập</span>
                            </a>
                            <a class="btn btn-danger" asp-controller="Account" asp-action="Register">
                                <i class="fas fa-user-plus me-1"></i>
                                <span class="d-none d-md-inline">Đăng ký</span>
                            </a>
                        }
                    </div>
                </div>
            </nav>


        </div>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    @TempData["InfoMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer">
        <div class="container-fluid px-4">
            <div class="d-flex justify-content-start align-items-center">
                <div>
                    © 2025 - <span style="color: #e50914; font-weight: 500;">CineZore</span> - <a asp-area="" asp-controller="Home" asp-action="Privacy" class="privacy-link" style="color:rgb(43, 71, 255); text-decoration: none;">Privacy</a>
                </div>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/theme-switcher.js" asp-append-version="true"></script>
    <script src="~/js/effects.js" asp-append-version="true"></script>

    <!-- Script để đánh dấu menu đang hoạt động -->
    <script>
        $(document).ready(function() {
            // Lấy đường dẫn hiện tại
            var currentPath = window.location.pathname.toLowerCase();

            // Đánh dấu menu đang hoạt động
            $('.nav-link').each(function() {
                var linkPath = $(this).attr('href');
                if (linkPath && currentPath.indexOf(linkPath.toLowerCase()) !== -1) {
                    $(this).addClass('active');
                }
            });

            // Đánh dấu menu đặc biệt
            if (currentPath.indexOf('/phim') !== -1) {
                $('a[href="/Phim"]').addClass('active');
            } else if (currentPath.indexOf('/lichchieu') !== -1) {
                $('a[href="/LichChieu"]').addClass('active');
            } else if (currentPath.indexOf('/datve') !== -1) {
                $('a[href="/DatVe"]').addClass('active');
            } else if (currentPath.indexOf('/search/moviesbydate') !== -1) {
                $('a[href="/Search/MoviesByDate"]').addClass('active');
            } else if (currentPath === '/' || currentPath === '/home' || currentPath === '/home/<USER>') {
                $('a[href="/"]').addClass('active');
            }
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)

    <!-- Nút chuyển đổi chế độ sáng/tối -->
    <button id="theme-toggle" class="theme-toggle" aria-label="Chuyển đổi chế độ sáng/tối">
        <i id="theme-icon" class="fas fa-moon"></i>
    </button>

    @if (User.Identity.IsAuthenticated)
    {
        <div class="feedback-icon" id="feedbackIcon">
            <i class="fas fa-envelope"></i>
        </div>
    }

    <!-- Feedback Form (Chat Bubble Style) -->
    <div class="feedback-panel" id="feedbackPanel">
        <div class="feedback-header">
            <h5>Gửi phản hồi</h5>
            <button type="button" class="btn-close" id="closeFeedback" aria-label="Close"></button>
        </div>
        <div class="feedback-body">
            <div class="chat-messages" id="chatMessages">
                <div class="chat-message received">
                    <div class="message-content">Xin chào! Bạn có điều gì muốn chia sẻ với chúng tôi không?</div>
                </div>
            </div>
            <form id="feedbackForm" asp-controller="Home" asp-action="SendFeedback" method="post" enctype="multipart/form-data">
                <div id="emailInputContainer" style="display: none;" class="email-input-container">
                    <input type="email" name="email" id="emailInput" class="chat-email-input" placeholder="Email của bạn" />
                </div>
                <div class="chat-input-container">
                    <textarea class="chat-input" id="message" name="message" placeholder="Nhập tin nhắn..." required></textarea>
                    <div class="chat-actions">
                        <label for="attachment" class="attachment-label">
                            <i class="fas fa-paperclip"></i>
                        </label>
                        <input type="file" class="d-none" id="attachment" name="attachment" accept="image/*,.pdf,.doc,.docx">
                        <button type="submit" class="chat-send-btn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
                <div id="attachmentPreview" class="attachment-preview"></div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const feedbackIcon = document.getElementById('feedbackIcon');
            const feedbackPanel = document.getElementById('feedbackPanel');
            const closeFeedback = document.getElementById('closeFeedback');
            const feedbackForm = document.getElementById('feedbackForm');
            const attachmentInput = document.getElementById('attachment');
            const attachmentPreview = document.getElementById('attachmentPreview');
            const chatMessages = document.getElementById('chatMessages');
            const messageInput = document.getElementById('message');
            const emailInputContainer = document.getElementById('emailInputContainer');

            // Kiểm tra xem tất cả các element cần thiết có tồn tại không
            if (!feedbackIcon || !feedbackPanel || !closeFeedback || !feedbackForm ||
                !attachmentInput || !attachmentPreview || !chatMessages || !messageInput || !emailInputContainer) {
                return; // Thoát nếu thiếu element nào đó
            }

            // Kiểm tra xem người dùng đã đăng nhập chưa
            const isAuthenticated = @(User.Identity.IsAuthenticated ? "true" : "false");

            if (!isAuthenticated) {
                emailInputContainer.style.display = 'block';

                // Thêm tin nhắn yêu cầu email
                const emailRequestElement = document.createElement('div');
                emailRequestElement.className = 'chat-message received';
                emailRequestElement.innerHTML = `<div class="message-content">Vui lòng cung cấp email để chúng tôi có thể liên hệ với bạn.</div>`;
                chatMessages.appendChild(emailRequestElement);
            }

            // Hiện/ẩn form phản hồi khi click vào icon
            feedbackIcon.addEventListener('click', function() {
                feedbackPanel.classList.toggle('active');
                if (feedbackPanel.classList.contains('active')) {
                    feedbackIcon.classList.add('hide');
                }
            });

            // Đóng form khi click vào nút close
            closeFeedback.addEventListener('click', function() {
                feedbackPanel.classList.remove('active');
                feedbackIcon.classList.remove('hide');
            });

            // Hiển thị file được chọn
            attachmentInput.addEventListener('change', function() {
                attachmentPreview.innerHTML = '';

                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    const filePreview = document.createElement('div');
                    filePreview.className = 'file-preview';

                    // Hiển thị xem trước cho ảnh
                    if (file.type.startsWith('image/')) {
                        const img = document.createElement('img');
                        img.src = URL.createObjectURL(file);
                        img.onload = function() {
                            URL.revokeObjectURL(this.src);
                        }
                        filePreview.appendChild(img);
                    } else {
                        // Hiển thị icon cho các loại file khác
                        const fileIcon = document.createElement('i');
                        fileIcon.className = 'fas fa-file';
                        filePreview.appendChild(fileIcon);
                    }

                    const fileName = document.createElement('span');
                    fileName.textContent = file.name;
                    filePreview.appendChild(fileName);

                    const removeBtn = document.createElement('button');
                    removeBtn.type = 'button';
                    removeBtn.className = 'remove-file';
                    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                    removeBtn.onclick = function() {
                        attachmentInput.value = '';
                        attachmentPreview.innerHTML = '';
                    };
                    filePreview.appendChild(removeBtn);

                    attachmentPreview.appendChild(filePreview);
                }
            });

            // Xử lý submit form
            feedbackForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // Kiểm tra email nếu chưa đăng nhập
                if (!isAuthenticated) {
                    const emailInput = document.getElementById('emailInput');
                    if (!emailInput.value.trim()) {
                        // Hiển thị thông báo lỗi
                        const errorElement = document.createElement('div');
                        errorElement.className = 'chat-message received';
                        errorElement.innerHTML = '<div class="message-content">Vui lòng nhập email của bạn để chúng tôi có thể liên hệ.</div>';
                        chatMessages.appendChild(errorElement);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                        return;
                    }
                }
                // Thêm tin nhắn của người dùng vào hộp chat
                const messageText = messageInput.value.trim();
                if (messageText) {
                    const messageElement = document.createElement('div');
                    messageElement.className = 'chat-message sent';
                    messageElement.innerHTML = `<div class="message-content">${messageText}</div>`;
                    chatMessages.appendChild(messageElement);
                    // Thêm ảnh nếu có
                    if (attachmentInput.files && attachmentInput.files[0]) {
                        const file = attachmentInput.files[0];
                        if (file.type.startsWith('image/')) {
                            const attachmentElement = document.createElement('div');
                            attachmentElement.className = 'chat-message sent';
                            attachmentElement.innerHTML = `<div class="message-content attachment-message"><img src="${URL.createObjectURL(file)}" alt="Attachment"></div>`;
                            chatMessages.appendChild(attachmentElement);
                        } else {
                            const attachmentElement = document.createElement('div');
                            attachmentElement.className = 'chat-message sent';
                            attachmentElement.innerHTML = `<div class="message-content attachment-message"><i class="fas fa-file"></i> ${file.name}</div>`;
                            chatMessages.appendChild(attachmentElement);
                        }
                    }
                    // Tự động cuộn xuống
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    // Hiển thị tin nhắn đang gửi
                    const loadingElement = document.createElement('div');
                    loadingElement.className = 'chat-message received';
                    loadingElement.innerHTML = `<div class="message-content"><div class="typing-indicator"><span></span><span></span><span></span></div></div>`;
                    chatMessages.appendChild(loadingElement);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    // Gửi form qua AJAX
                    const formData = new FormData(feedbackForm);
                    fetch(feedbackForm.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        // Xóa tin nhắn loading
                        chatMessages.removeChild(loadingElement);
                        // Hiển thị tin nhắn phản hồi
                        const responseElement = document.createElement('div');
                        responseElement.className = 'chat-message received';
                        responseElement.innerHTML = '<div class="message-content">Cảm ơn bạn đã gửi phản hồi! Chúng tôi sẽ phản hồi sớm nhất có thể.</div>';
                        chatMessages.appendChild(responseElement);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                        // Reset form
                        messageInput.value = '';
                        attachmentInput.value = '';
                        attachmentPreview.innerHTML = '';
                    })
                    .catch(error => {
                        // Xóa tin nhắn loading
                        chatMessages.removeChild(loadingElement);
                        // Hiển thị thông báo lỗi
                        const errorElement = document.createElement('div');
                        errorElement.className = 'chat-message received';
                        errorElement.innerHTML = '<div class="message-content">Có lỗi xảy ra khi gửi phản hồi. Vui lòng thử lại sau!</div>';
                        chatMessages.appendChild(errorElement);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    });
                }
            });

            // Đóng panel khi click ra ngoài
            document.addEventListener('click', function(e) {
                if (!feedbackPanel.contains(e.target) && !feedbackIcon.contains(e.target) && feedbackPanel.classList.contains('active')) {
                    feedbackPanel.classList.remove('active');
                    feedbackIcon.classList.remove('hide');
                }
            });
        });
    </script>
</body>
</html>
