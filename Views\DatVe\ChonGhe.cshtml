@model CinemaBooking.Models.LichChieu

@{
    ViewData["Title"] = "Chọn ghế";
}

<div class="container-fluid mt-4 px-4">
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0 rounded-3 overflow-hidden">
                <div class="card-header bg-gradient-primary text-white p-4">
                    <div class="d-flex align-items-center">
                        <div class="movie-icon me-3">
                            <i class="fas fa-film fa-2x"></i>
                        </div>
                        <div>
                            <h3 class="mb-1 fw-bold">@Model.Phim.TenPhim</h3>
                            <p class="mb-0 opacity-75">
                                <i class="fas fa-clock me-1"></i>
                                @Model.Phim.ThoiLuong phút | @Model.Phim.TheLoai
                            </p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="info-icon me-3">
                                        <i class="fas fa-building text-primary"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">Rạp chiếu</small>
                                        <strong class="fs-6">@Model.PhongChieu.RapPhim.TenRap</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="d-flex align-items-center">
                                    <div class="info-icon me-3">
                                        <i class="fas fa-door-open text-primary"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">Phòng chiếu</small>
                                        <strong class="fs-6">Phòng @Model.PhongChieu.SoPhong</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="info-icon me-3">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">Ngày chiếu</small>
                                        <strong class="fs-6">@Model.NgayChieu.ToString("dd/MM/yyyy")</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="d-flex align-items-center">
                                    <div class="info-icon me-3">
                                        <i class="fas fa-clock text-primary"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">Giờ chiếu</small>
                                        <strong class="fs-6">@($"{Model.GioChieu.Hours:D2}:{Model.GioChieu.Minutes:D2}")</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="seat-selection-section">
                        <div class="section-header mb-4">
                            <h4 class="fw-bold text-dark mb-2">
                                <i class="fas fa-chair me-2 text-primary"></i>
                                Chọn ghế ngồi
                            </h4>
                            <div class="capacity-info p-3 bg-light rounded-3 border-start border-primary border-4">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="capacity-item">
                                            <i class="fas fa-users text-primary mb-1"></i>
                                            <div class="fw-bold text-primary">@Model.PhongChieu.SucChua</div>
                                            <small class="text-muted">Tổng ghế</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="capacity-item">
                                            <i class="fas fa-grip-lines text-primary mb-1"></i>
                                            <div class="fw-bold text-primary">@(ViewBag.HangGhe.Length)</div>
                                            <small class="text-muted">Số hàng</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="capacity-item">
                                            <i class="fas fa-grip-vertical text-primary mb-1"></i>
                                            <div class="fw-bold text-primary">@ViewBag.SoCot</div>
                                            <small class="text-muted">Số cột</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="screen-container mb-4">
                            <div class="screen-wrapper">
                                <div class="screen-label mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-arrow-down me-1"></i>
                                        Hướng màn hình
                                    </small>
                                </div>
                                <div class="screen">
                                    <i class="fas fa-desktop me-2"></i>
                                    MÀN HÌNH CHIẾU
                                </div>
                                <div class="screen-glow"></div>
                                <div class="screen-reflection"></div>
                            </div>
                        </div>
                        
                        <div class="seat-map-container">
                            <table class="table-seats">
                                @{
                                    var gheDaDat = ViewBag.GheDaDat as List<string> ?? new List<string>();
                                    var hangGhe = ViewBag.HangGhe as char[] ?? new[] { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J' };
                                    var soCot = ViewBag.SoCot as int? ?? 12;
                                    
                                    <!-- Thêm hàng hiển thị số cột -->
                                    <tr>
                                        <td></td> <!-- Ô trống ở góc trái trên -->
                                        @for (int i = 1; i <= soCot; i++)
                                        {
                                            <td class="col-label">@i</td>
                                        }
                                    </tr>
                                    
                                    foreach (var row in hangGhe)
                                    {
                                        <tr>
                                            <td class="row-label">@row</td>
                                            @for (int i = 1; i <= soCot; i++)
                                            {
                                                string seatId = $"{row}{i}";
                                                bool isBooked = gheDaDat.Contains(seatId);
                                                string seatType = "seat-regular";
                                                
                                                // Xác định loại ghế theo vị trí
                                                int hangIndex = Array.IndexOf(hangGhe, row);
                                                int tongSoHang = hangGhe.Length;
                                                
                                                if (hangIndex >= tongSoHang * 0.7) 
                                                {
                                                    // VIP: 30% hàng cuối
                                                    seatType = "seat-vip";
                                                }
                                                
                                                // Sweetbox: Hàng cuối, 4 ghế đầu tiên
                                                if (row == hangGhe[hangGhe.Length - 1] && i <= 4)
                                                {
                                                    seatType = "seat-sweetbox";
                                                }
                                                
                                                <td>
                                                    <button class="seat @seatType @(isBooked ? "seat-booked" : "")" 
                                                        data-seat="@seatId" 
                                                        data-type="@seatType" 
                                                        data-price="@(seatType == "seat-vip" ? Model.GiaVe * 1.2m : (seatType == "seat-sweetbox" ? Model.GiaVe * 1.5m : Model.GiaVe))"
                                                        @(isBooked ? "disabled" : "")>
                                                        @i
                                                    </button>
                                                </td>
                                            }
                                        </tr>
                                    }
                                }
                            </table>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="maKhuyenMai" class="form-label">Mã khuyến mãi (nếu có)</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="maKhuyenMai" name="maKhuyenMai">
                            <button class="btn btn-outline-secondary" type="button" id="btnKiemTraKM">Kiểm tra</button>
                        </div>
                        <div id="khuyenMaiMessage" class="mt-2"></div>
                    </div>


                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card shadow-lg border-0 rounded-3 sticky-top" style="top: 2rem;">
                <div class="card-header bg-gradient-info text-white p-4">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-info-circle me-2"></i>
                        Thông tin đặt vé
                    </h5>
                </div>
                <div class="card-body p-4">
                    <!-- Chú thích ghế -->
                    <div class="legend-section mb-4">
                        <h6 class="fw-bold mb-3 text-dark">
                            <i class="fas fa-palette me-2"></i>
                            Chú thích
                        </h6>
                        <div class="legend-grid">
                            <div class="legend-item">
                                <div class="seat seat-regular legend-seat"></div>
                                <span class="legend-text">Ghế thường</span>
                            </div>
                            <div class="legend-item">
                                <div class="seat seat-vip legend-seat"></div>
                                <span class="legend-text">Ghế VIP</span>
                            </div>
                            <div class="legend-item">
                                <div class="seat seat-sweetbox legend-seat"></div>
                                <span class="legend-text">Sweetbox</span>
                            </div>
                            <div class="legend-item">
                                <div class="seat seat-selected legend-seat"></div>
                                <span class="legend-text">Ghế đã chọn</span>
                            </div>
                            <div class="legend-item">
                                <div class="seat seat-booked legend-seat"></div>
                                <span class="legend-text">Ghế đã đặt</span>
                            </div>
                            <div class="legend-item">
                                <div class="seat seat-booked seat-newly-booked legend-seat"></div>
                                <span class="legend-text">Ghế vừa được đặt</span>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Thông tin ghế đã chọn -->
                    <div class="selected-info">
                        <h6 class="fw-bold mb-3 text-dark">
                            <i class="fas fa-check-circle me-2"></i>
                            Ghế đã chọn
                        </h6>
                        <div id="seatSelectionList" class="selected-seats-display mb-3">
                            <div class="empty-selection">
                                <i class="fas fa-chair text-muted"></i>
                                <span class="text-muted">Chưa chọn ghế nào</span>
                            </div>
                        </div>

                        <!-- Mã khuyến mãi -->
                        <div class="promotion-section mb-4">
                            <label for="maKhuyenMai" class="form-label fw-semibold">
                                <i class="fas fa-gift me-1"></i>
                                Mã khuyến mãi
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="maKhuyenMai" name="maKhuyenMai" placeholder="Nhập mã khuyến mãi...">
                                <button class="btn btn-outline-primary" type="button" id="btnKiemTraKM">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div id="khuyenMaiMessage" class="mt-2"></div>
                            <small class="text-muted">Nhập mã để nhận ưu đãi</small>
                        </div>

                        <!-- Tổng tiền -->
                        <div class="total-section bg-light rounded-3 p-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">Giá vé:</span>
                                <span class="fw-semibold">@string.Format("{0:N0} VNĐ", Model.GiaVe)</span>
                            </div>
                            <hr class="my-2">
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold fs-5">Tổng tiền:</span>
                                <span id="totalAmount" class="text-primary fw-bold fs-5">0 VNĐ</span>
                            </div>
                        </div>

                        <form id="bookingForm" asp-action="LuuDatVe" method="post">
                            <input type="hidden" name="maLichChieu" value="@Model.MaLichChieu">
                            <input type="hidden" name="selectedSeats" id="selectedSeats">
                            <input type="hidden" name="maKhuyenMai" id="khuyenMaiHidden">
                            <button type="submit" class="btn btn-success btn-lg w-100 mt-4 book-button" id="bookButton" disabled>
                                <i class="fas fa-ticket-alt me-2"></i>
                                Đặt vé ngay
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        /* Gradient backgrounds */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        /* Movie info styling */
        .movie-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .info-item {
            transition: transform 0.2s ease;
        }

        .info-item:hover {
            transform: translateX(5px);
        }

        .info-icon {
            width: 40px;
            height: 40px;
            background: rgba(0, 123, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Capacity info styling */
        .capacity-item {
            text-align: center;
        }

        .capacity-item i {
            font-size: 1.2rem;
        }

        /* Screen styling */
        .screen-container {
            text-align: center;
            margin-bottom: 40px;
            perspective: 1000px;
            padding: 20px 0;
        }

        .screen-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
            max-width: 600px;
        }

        .screen {
            display: inline-block;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #ffffff;
            width: 90%;
            height: 50px;
            text-align: center;
            line-height: 50px;
            border-top-left-radius: 80px;
            border-top-right-radius: 80px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            letter-spacing: 3px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
            box-shadow:
                0 12px 24px rgba(0,0,0,0.4),
                0 0 30px rgba(255, 255, 255, 0.1),
                inset 0 2px 4px rgba(255, 255, 255, 0.1),
                inset 0 -2px 4px rgba(0, 0, 0, 0.3);
            transform: rotateX(12deg);
            position: relative;
            z-index: 2;
            border: 2px solid #444;
        }

        .screen::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 10%;
            right: 10%;
            height: 3px;
            background: linear-gradient(90deg, transparent, #666, transparent);
            border-radius: 2px;
        }

        .screen::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 15%;
            right: 15%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #333, transparent);
            border-radius: 1px;
        }

        .screen-label {
            text-align: center;
            margin-bottom: 10px;
        }

        .screen-glow {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            width: 95%;
            height: 30px;
            background: linear-gradient(to bottom,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(200, 200, 200, 0.05) 50%,
                transparent 100%);
            border-radius: 50%;
            filter: blur(15px);
            z-index: 1;
        }

        .screen-reflection {
            position: absolute;
            top: 105%;
            left: 50%;
            transform: translateX(-50%) rotateX(180deg) scaleY(0.3);
            width: 90%;
            height: 50px;
            background: linear-gradient(135deg,
                rgba(26, 26, 26, 0.3) 0%,
                rgba(45, 45, 45, 0.2) 50%,
                rgba(26, 26, 26, 0.1) 100%);
            border-top-left-radius: 80px;
            border-top-right-radius: 80px;
            opacity: 0.4;
            filter: blur(2px);
            z-index: 0;
        }
        
        /* Seat map container */
        .seat-map-container {
            max-height: 500px;
            overflow-y: auto;
            overflow-x: auto;
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            display: flex;
            justify-content: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-seats {
            margin: 0 auto;
            border-collapse: separate;
            border-spacing: 4px;
        }

        .table-seats td {
            padding: 2px;
            text-align: center;
        }

        .row-label {
            font-weight: bold;
            width: 30px;
            min-width: 30px;
            font-size: 14px;
            vertical-align: middle;
            color: #495057;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            height: 30px;
            line-height: 30px;
            text-align: center;
        }

        .col-label {
            font-weight: bold;
            height: 25px;
            font-size: 12px;
            color: #6c757d;
            padding-bottom: 5px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 4px;
            line-height: 20px;
        }
        
        /* Seat styling */
        .seat {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 8px;
            color: #333;
            font-weight: bold;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
            position: relative;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .seat:hover:not(.seat-booked) {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .seat-regular {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            color: #495057;
            border: 2px solid #dee2e6;
        }

        .seat-regular:hover:not(.seat-booked) {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-color: #adb5bd;
        }

        .seat-vip {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            color: white;
            border: 2px solid #ff8f00;
        }

        .seat-vip:hover:not(.seat-booked) {
            background: linear-gradient(135deg, #ff8f00 0%, #e65100 100%);
        }

        .seat-sweetbox {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            color: white;
            border: 2px solid #ad1457;
        }

        .seat-sweetbox:hover:not(.seat-booked) {
            background: linear-gradient(135deg, #ad1457 0%, #880e4f 100%);
        }

        .seat-selected {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            transform: scale(1.15);
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.6);
            border: 2px solid #0056b3;
            z-index: 10;
        }

        .seat-booked {
            background: linear-gradient(135deg, #dc3545 0%, #a71e2a 100%);
            color: white;
            cursor: not-allowed;
            border: 2px solid #a71e2a;
            opacity: 0.7;
        }
        
        .seat-newly-booked {
            animation: pulse 1.5s infinite;
            box-shadow: 0 0 15px rgba(255, 0, 0, 0.8);
            z-index: 10;
            position: relative;
        }
        
        @@keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .seat-hidden {
            visibility: hidden;
        }
        
        /* Legend and sidebar styling */
        .legend-grid {
            display: grid;
            gap: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .legend-item:hover {
            background: rgba(233, 236, 239, 0.9);
            transform: translateX(3px);
        }

        .legend-seat {
            width: 24px !important;
            height: 24px !important;
            display: inline-block;
            margin-right: 12px;
            border-radius: 6px !important;
        }

        .legend-text {
            font-weight: 500;
            color: #495057;
        }

        .selected-seats-display {
            min-height: 60px;
            max-height: 200px;
            overflow-y: auto;
            background: rgba(248, 249, 250, 0.5);
            border-radius: 8px;
            padding: 12px;
        }

        .empty-selection {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .empty-selection i {
            font-size: 2rem;
            margin-bottom: 8px;
            display: block;
        }

        .promotion-section .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.2s ease;
        }

        .promotion-section .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .total-section {
            border: 2px solid #e9ecef;
        }

        .book-button {
            border-radius: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .book-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
        }

        .book-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        /* Responsive design */
        @@media (max-width: 768px) {
            .container-fluid {
                padding: 0 15px;
            }

            .seat {
                width: 22px;
                height: 22px;
                font-size: 9px;
                margin: 1px;
            }

            .row-label, .col-label {
                font-size: 10px;
                width: 22px;
                min-width: 22px;
            }

            .table-seats {
                border-spacing: 2px;
            }

            .movie-icon {
                width: 50px;
                height: 50px;
            }

            .info-icon {
                width: 35px;
                height: 35px;
            }

            .screen {
                height: 40px;
                line-height: 40px;
                font-size: 14px;
                letter-spacing: 2px;
            }

            .screen-container {
                margin-bottom: 30px;
                padding: 15px 0;
            }

            .card-header {
                padding: 1rem !important;
            }

            .card-body {
                padding: 1rem !important;
            }
        }

        @@media (max-width: 576px) {
            .seat {
                width: 18px;
                height: 18px;
                font-size: 8px;
                margin: 0.5px;
            }

            .row-label, .col-label {
                font-size: 8px;
                width: 18px;
                min-width: 18px;
            }

            .table-seats {
                border-spacing: 1px;
            }

            .legend-item {
                padding: 6px 8px;
            }

            .legend-seat {
                width: 20px !important;
                height: 20px !important;
            }

            .screen {
                height: 35px;
                line-height: 35px;
                font-size: 12px;
                letter-spacing: 1px;
            }

            .screen-container {
                margin-bottom: 25px;
                padding: 10px 0;
            }

            .screen-reflection {
                display: none; /* Ẩn reflection trên mobile để tối ưu hiệu suất */
            }
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            var selectedSeats = [];
            var promotionValue = 0;
            var promotionCode = "";
            var promotionDiscount = 0;

            // Khởi tạo tổng tiền ban đầu
            updateTotalAmount();
            
            // Điều chỉnh vị trí cuộn của seat-map-container
            adjustSeatMapPosition();
            
            // Xử lý chọn ghế
            $('.seat:not(.seat-booked)').on('click', function() {
                var seat = $(this).data('seat');
                var price = $(this).data('price');
                var type = $(this).data('type');
                
                if ($(this).hasClass('seat-selected')) {
                    // Bỏ chọn ghế
                    $(this).removeClass('seat-selected');
                    selectedSeats = selectedSeats.filter(s => s.id !== seat);
                } else {
                    // Chọn ghế
                    $(this).addClass('seat-selected');
                    selectedSeats.push({
                        id: seat,
                        price: price,
                        type: type
                    });
                }
                
                updateSeatSelection();
                updateTotalAmount();
            });
            
            // Xử lý kiểm tra mã khuyến mãi
            $('#btnKiemTraKM').on('click', function() {
                var maKM = $('#maKhuyenMai').val().trim();
                if (!maKM) {
                    $('#khuyenMaiMessage').html('<span class="text-danger">Vui lòng nhập mã khuyến mãi</span>');
                    return;
                }
                
                // Gọi API kiểm tra khuyến mãi
                $.ajax({
                    url: '@Url.Action("KiemTraKhuyenMai", "DatVe")',
                    type: 'POST',
                    data: { maKhuyenMai: maKM },
                    success: function(response) {
                        if (response.isValid) {
                            $('#khuyenMaiMessage').html('<span class="text-success">Mã hợp lệ: Giảm ' + response.phanTramGiam + '%</span>');
                            promotionCode = maKM;
                            promotionDiscount = response.phanTramGiam;
                            $('#khuyenMaiHidden').val(maKM);
                            updateTotalAmount();
                        } else {
                            $('#khuyenMaiMessage').html('<span class="text-danger">Mã không hợp lệ hoặc đã hết hạn</span>');
                            promotionCode = "";
                            promotionDiscount = 0;
                            $('#khuyenMaiHidden').val('');
                            updateTotalAmount();
                        }
                    },
                    error: function() {
                        $('#khuyenMaiMessage').html('<span class="text-danger">Lỗi khi kiểm tra mã</span>');
                    }
                });
            });
            
            // Cập nhật danh sách ghế đã chọn
            function updateSeatSelection() {
                var seatList = $('#seatSelectionList');
                
                if (selectedSeats.length === 0) {
                    seatList.html('<p>Chưa chọn ghế nào</p>');
                    $('#bookButton').prop('disabled', true);
                    $('#selectedSeats').val('');
                } else {
                    var html = '<ul class="list-group list-group-flush">';
                    var seatIds = [];
                    
                    selectedSeats.forEach(function(seat) {
                        var seatTypeText = seat.type === 'seat-vip' ? 'VIP' : 
                                          (seat.type === 'seat-sweetbox' ? 'Sweetbox' : 'Thường');
                        html += '<li class="list-group-item py-1 px-2 d-flex justify-content-between align-items-center">' + 
                                '<span>Ghế ' + seat.id + ' <small>(' + seatTypeText + ')</small></span>' +
                                '<span>' + formatCurrency(seat.price) + '</span>' +
                                '</li>';
                        seatIds.push(seat.id);
                    });
                    
                    html += '</ul>';
                    seatList.html(html);
                    $('#bookButton').prop('disabled', false);
                    $('#selectedSeats').val(seatIds.join(','));
                }
            }
            
            // Cập nhật tổng tiền
            function updateTotalAmount() {
                var total = 0;

                selectedSeats.forEach(function(seat) {
                    var price = parseFloat(seat.price);
                    if (!isNaN(price)) {
                        total += price;
                    }
                });

                // Áp dụng khuyến mãi nếu có
                if (promotionDiscount > 0) {
                    var discount = (total * promotionDiscount) / 100;
                    total = total - discount;
                }

                $('#totalAmount').text(formatCurrency(total));
            }
            
            // Định dạng tiền tệ
            function formatCurrency(amount) {
                return new Intl.NumberFormat('vi-VN', { style: 'decimal' }).format(amount) + ' VNĐ';
            }
            
            // Điều chỉnh vị trí cuộn của seat-map-container để hiển thị ở giữa
            function adjustSeatMapPosition() {
                var container = $('.seat-map-container');
                var tableWidth = $('.table-seats').width();
                var containerWidth = container.width();
                
                if (tableWidth < containerWidth) {
                    // Nếu bảng ghế nhỏ hơn container, căn giữa
                    container.scrollLeft(0);
                } else {
                    // Nếu bảng ghế lớn hơn container, cuộn đến giữa
                    container.scrollLeft((tableWidth - containerWidth) / 2);
                }
                
                // Thiết lập sự kiện scroll mượt mà
                container.css('scroll-behavior', 'smooth');
            }
            
            // Ẩn thanh cuộn ngang khi không cần thiết
            $(window).on('load resize', function() {
                adjustSeatMapPosition();
            });
            
            // Chạy lại kiểm tra trạng thái ghế định kỳ
            var seatCheckInterval = setInterval(checkBookedSeats, 30000); // mỗi 30 giây
            
            // Kiểm tra ghế đã đặt từ server
            function checkBookedSeats() {
                $.ajax({
                    url: '@Url.Action("GetBookedSeats", "DatVe")',
                    type: 'GET',
                    data: { maLichChieu: @Model.MaLichChieu },
                    success: function(response) {
                        if (response.success) {
                            var bookedSeats = response.bookedSeats;
                            var currentlySelected = selectedSeats.map(s => s.id);
                            var newlyBooked = [];
                            
                            // Tìm các ghế vừa được đặt
                            bookedSeats.forEach(function(seat) {
                                if (currentlySelected.includes(seat)) {
                                    newlyBooked.push(seat);
                                }
                                
                                var seatElement = $('.seat[data-seat="' + seat + '"]');
                                if (!seatElement.hasClass('seat-booked')) {
                                    seatElement.addClass('seat-booked');
                                    if (!seatElement.hasClass('seat-newly-booked')) {
                                        seatElement.addClass('seat-newly-booked');
                                    }
                                    seatElement.prop('disabled', true);
                                }
                            });
                            
                            // Nếu có ghế vừa được người khác đặt, cập nhật lại danh sách
                            if (newlyBooked.length > 0) {
                                selectedSeats = selectedSeats.filter(function(seat) {
                                    return !newlyBooked.includes(seat.id);
                                });
                                
                                $('.seat-selected').each(function() {
                                    var seatId = $(this).data('seat');
                                    if (newlyBooked.includes(seatId)) {
                                        $(this).removeClass('seat-selected');
                                    }
                                });
                                
                                updateSeatSelection();
                                updateTotalAmount();
                                
                                if (newlyBooked.length === 1) {
                                    alert('Ghế ' + newlyBooked[0] + ' vừa được người khác đặt. Vui lòng chọn ghế khác.');
                                } else {
                                    alert('Các ghế ' + newlyBooked.join(', ') + ' vừa được người khác đặt. Vui lòng chọn ghế khác.');
                                }
                            }
                            
                            setTimeout(function() {
                                $('.seat-newly-booked').removeClass('seat-newly-booked');
                            }, 5000);
                        }
                    }
                });
            }
            
            // Kiểm tra khi submit form
            $('#bookingForm').on('submit', function(e) {
                if (selectedSeats.length === 0) {
                    e.preventDefault();
                    alert('Vui lòng chọn ít nhất một ghế.');
                    return false;
                }
                return true;
            });
        });
    </script>
} 