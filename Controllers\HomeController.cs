using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using CinemaBooking.Models;
using CinemaBooking.Data;
using CinemaBooking.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Hosting;
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using MimeKit;
using MailKit.Net.Smtp;
using MailKit.Security;

namespace CinemaBooking.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public HomeController(ILogger<HomeController> logger, ApplicationDbContext context, IWebHostEnvironment webHostEnvironment)
        {
            _logger = logger;
            _context = context;
            _webHostEnvironment = webHostEnvironment;
        }

        public async Task<IActionResult> Index(int page = 1)
        {
            const int pageSize = 10; // 5x2 = 10 phim mỗi trang

            // Tính toán phân trang
            var totalMoviesCount = await _context.Phims.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalMoviesCount / pageSize);
            var skip = (page - 1) * pageSize;

            // Đảm bảo page hợp lệ
            if (page < 1) page = 1;
            if (page > totalPages && totalPages > 0) page = totalPages;

            // Lấy danh sách phim mới nhất với phân trang
            var latestMovies = await _context.Phims
                .Include(p => p.DanhGias)
                .OrderByDescending(p => p.NgayPhatHanh)
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync();

            // Lấy danh sách phim được đánh giá cao (chỉ phim có điểm từ 4.0 trở lên)
            var topRatedMovies = await _context.Phims
                .Where(p => _context.DanhGias.Where(d => d.MaPhim == p.MaPhim).Any() &&
                           _context.DanhGias.Where(d => d.MaPhim == p.MaPhim).Average(d => d.DiemSo ?? 0) >= 4.0)
                .OrderByDescending(p => _context.DanhGias.Where(d => d.MaPhim == p.MaPhim).Average(d => d.DiemSo ?? 0))
                .Take(10)
                .Include(p => p.DanhGias)
                .ToListAsync();

            // Lấy danh sách phim nổi bật (phim có lịch chiếu trong tuần tới)
            var featuredMovies = await _context.Phims
                .Include(p => p.DanhGias)
                .Include(p => p.LichChieus)
                .Where(p => p.LichChieus.Any(l => l.NgayChieu >= DateTime.Today && l.NgayChieu <= DateTime.Today.AddDays(7)))
                .OrderByDescending(p => p.LichChieus.Count(l => l.NgayChieu >= DateTime.Today))
                .Take(6)
                .ToListAsync();

            // Lấy thống kê tổng quan
            var totalMovies = await _context.Phims.CountAsync();
            var totalCinemas = await _context.RapPhims.CountAsync();
            var totalBookings = await _context.DatVes.CountAsync();

            // Truyền dữ liệu vào ViewBag
            ViewBag.TopRatedMovies = topRatedMovies;
            ViewBag.FeaturedMovies = featuredMovies;
            ViewBag.TotalMovies = totalMovies;
            ViewBag.TotalCinemas = totalCinemas;
            ViewBag.TotalBookings = totalBookings;

            // Thông tin phân trang
            ViewBag.CurrentPage = page;
            ViewBag.TotalPages = totalPages;
            ViewBag.TotalMoviesCount = totalMoviesCount;
            ViewBag.PageSize = pageSize;

            return View(latestMovies);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        public IActionResult TestFileAccess()
        {
            var model = new Dictionary<string, object>();
            try
            {
                var webRootPath = _webHostEnvironment.WebRootPath;
                var postersPath = Path.Combine(webRootPath, "posters");
                var trailersPath = Path.Combine(webRootPath, "trailers");

                model.Add("WebRootPath", webRootPath);

                // Kiểm tra thư mục poster
                if (!Directory.Exists(postersPath))
                {
                    try
                    {
                        Directory.CreateDirectory(postersPath);
                        model.Add("PostersFolder", $"Thư mục posters không tồn tại, đã tạo thư mục mới tại {postersPath}");
                    }
                    catch (Exception ex)
                    {
                        model.Add("PostersError", $"Không thể tạo thư mục posters: {ex.Message}");
                    }
                }
                else
                {
                    model.Add("PostersFolder", $"Thư mục posters tồn tại tại {postersPath}");

                    // Kiểm tra quyền ghi
                    try
                    {
                        var testFile = Path.Combine(postersPath, "test.txt");
                        System.IO.File.WriteAllText(testFile, "Test file permission");
                        model.Add("PostersWriteAccess", "Có quyền ghi vào thư mục posters");

                        // Xóa file test
                        System.IO.File.Delete(testFile);
                    }
                    catch (Exception ex)
                    {
                        model.Add("PostersWriteError", $"Không có quyền ghi vào thư mục posters: {ex.Message}");
                    }
                }

                // Kiểm tra thư mục trailers
                if (!Directory.Exists(trailersPath))
                {
                    try
                    {
                        Directory.CreateDirectory(trailersPath);
                        model.Add("TrailersFolder", $"Thư mục trailers không tồn tại, đã tạo thư mục mới tại {trailersPath}");
                    }
                    catch (Exception ex)
                    {
                        model.Add("TrailersError", $"Không thể tạo thư mục trailers: {ex.Message}");
                    }
                }
                else
                {
                    model.Add("TrailersFolder", $"Thư mục trailers tồn tại tại {trailersPath}");

                    // Kiểm tra quyền ghi
                    try
                    {
                        var testFile = Path.Combine(trailersPath, "test.txt");
                        System.IO.File.WriteAllText(testFile, "Test file permission");
                        model.Add("TrailersWriteAccess", "Có quyền ghi vào thư mục trailers");

                        // Xóa file test
                        System.IO.File.Delete(testFile);
                    }
                    catch (Exception ex)
                    {
                        model.Add("TrailersWriteError", $"Không có quyền ghi vào thư mục trailers: {ex.Message}");
                    }
                }

                model.Add("Success", true);
            }
            catch (Exception ex)
            {
                model.Add("Error", ex.Message);
                model.Add("Success", false);
            }

            return Json(model);
        }

        [Authorize]
        public async Task<IActionResult> LichSuDatVe()
        {
            // Lấy mã người dùng hiện tại
            var maNguoiDung = await User.GetLegacyUserIdAsync(_context);

            if (!maNguoiDung.HasValue)
            {
                return RedirectToAction("Login", "Account");
            }

            // Lấy danh sách đặt vé của người dùng
            var datVes = await _context.DatVes
                .Include(d => d.LichChieu)
                    .ThenInclude(l => l.Phim)
                .Include(d => d.LichChieu)
                    .ThenInclude(l => l.PhongChieu)
                        .ThenInclude(p => p.RapPhim)
                .Include(d => d.DatVeGhes)
                    .ThenInclude(dg => dg.Ghe)
                .Include(d => d.KhuyenMai)
                .Where(d => d.MaNguoiDung == maNguoiDung.Value)
                .OrderByDescending(d => d.NgayDat)
                .ToListAsync();

            return View(datVes);
        }

        // Trang hướng dẫn in vé và quét mã QR
        public IActionResult TicketHelp()
        {
            return View();
        }

        public IActionResult MomoSandboxGuide()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> SendFeedback(IFormFile attachment)
        {
            try
            {
                _logger.LogInformation("Bắt đầu xử lý gửi phản hồi");

                // In ra toàn bộ claim để debug
                foreach (var claim in User.Claims)
                {
                    _logger.LogInformation($"CLAIM TYPE: {claim.Type} - VALUE: {claim.Value}");
                }

                var name = User.GetUserFullName();
                var email = User.GetUserEmail();

                if (string.IsNullOrEmpty(email) && Request.Form.ContainsKey("email"))
                {
                    email = Request.Form["email"].ToString();
                    _logger.LogInformation($"Đã lấy email từ form: {email}");
                }
                if (string.IsNullOrEmpty(email))
                {
                    email = "<EMAIL>";
                    _logger.LogInformation("Không tìm thấy email, sử dụng email mặc định");
                }
                var message = Request.Form["message"].ToString();
                _logger.LogInformation($"Thông tin người gửi: {name} ({email})");
                _logger.LogInformation($"Nội dung phản hồi: {message}");
                var emailMessage = new MimeMessage();
                emailMessage.From.Add(new MailboxAddress("CineZore", "<EMAIL>"));
                emailMessage.To.Add(new MailboxAddress("CineZore Support", "<EMAIL>"));
                emailMessage.Subject = $"Phản hồi từ {name}";
                var builder = new BodyBuilder();

                builder.HtmlBody = $@"
<html>
<head>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #E30613; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .footer {{ text-align: center; padding: 20px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h2>CineZore - Phản hồi từ khách hàng</h2>
        </div>
        <div class='content'>
            <p><strong>Từ:</strong> {name}</p>
            <p><strong>Email:</strong> {email}</p>
            <p><strong>Thời gian:</strong> {DateTime.Now:dd/MM/yyyy HH:mm:ss}</p>
            <hr>
            <p><strong>Nội dung phản hồi:</strong></p>
            <p>{message.Replace("\n", "<br>")}</p>
        </div>
        <div class='footer'>
            <p>Email này được gửi tự động từ hệ thống CineZore</p>
            <p>© {DateTime.Now.Year} CineZore - Hệ thống đặt vé xem phim trực tuyến</p>
        </div>
    </div>
</body>
</html>";

                if (attachment != null && attachment.Length > 0)
                {
                    _logger.LogInformation($"Có file đính kèm: {attachment.FileName}, kích thước: {attachment.Length} bytes");
                    using (var ms = new MemoryStream())
                    {
                        await attachment.CopyToAsync(ms);
                        builder.Attachments.Add(attachment.FileName, ms.ToArray());
                    }
                }

                emailMessage.Body = builder.ToMessageBody();

                _logger.LogInformation("Đang kết nối đến SMTP server...");
                using (var smtp = new SmtpClient())
                {
                    try
                    {
                        await smtp.ConnectAsync("smtp.gmail.com", 587, SecureSocketOptions.StartTls);
                        _logger.LogInformation("Đã kết nối thành công đến SMTP server");

                        _logger.LogInformation("Đang xác thực...");
                        await smtp.AuthenticateAsync("<EMAIL>", "vclh bfyf onsw lauh");
                        _logger.LogInformation("Xác thực thành công");

                        _logger.LogInformation("Đang gửi email...");
                        await smtp.SendAsync(emailMessage);
                        _logger.LogInformation("Gửi email thành công");

                        await smtp.DisconnectAsync(true);
                        _logger.LogInformation("Đã ngắt kết nối SMTP");
                    }
                    catch (Exception smtpEx)
                    {
                        _logger.LogError($"Lỗi SMTP: {smtpEx.Message}");
                        _logger.LogError($"Stack trace: {smtpEx.StackTrace}");
                        throw;
                    }
                }

                // Gửi email cho admin
                try
                {
                    var confirmMessage = new MimeMessage();
                    confirmMessage.From.Add(new MailboxAddress("CineZore", "<EMAIL>"));
                    confirmMessage.To.Add(new MailboxAddress(name, email));
                    confirmMessage.Subject = "CineZore - Xác nhận đã nhận phản hồi của bạn";
                    var confirmBuilder = new BodyBuilder();
                    confirmBuilder.HtmlBody = $@"
<html>
<head>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #E30613; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .footer {{ text-align: center; padding: 20px; font-size: 12px; color: #666; }}
        .logo {{ height: 50px; margin-bottom: 10px; }}
        .info {{ font-size: 13px; color: #888; margin-top: 20px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <img src='https://i.imgur.com/1Q9Z1Zm.png' alt='CineZore' class='logo'>
            <h2>CineZore - Xác nhận đã nhận phản hồi của bạn</h2>
        </div>
        <div class='content'>
            <p>Xin chào <b>{name}</b>,</p>
            <p>Cảm ơn bạn đã gửi phản hồi đến CineZore!</p>
            <p>CineZore đã nhận được phản hồi từ bạn với nội dung:</p>
            <blockquote style='background:#fff3f3;border-left:4px solid #E30613;padding:10px 15px;margin:10px 0;color:#E30613;font-style:italic;'>
                {message.Replace("\n", "<br>")}
            </blockquote>
            <p>Chúng tôi sẽ xem xét và phản hồi trong thời gian sớm nhất. Nếu cần hỗ trợ gấp, bạn có thể liên hệ trực tiếp qua email này, số hotline <b>1900 1234</b> hoặc truy cập <a href='https://cinezore.com'>cinezore.com</a>.</p>
            <p style='color:gray;font-size:12px;'>Nếu bạn không gửi phản hồi này, vui lòng bỏ qua email này.</p>
            <p>Trân trọng,<br>Đội ngũ CineZore</p>
        </div>
        <div class='footer'>
            <div class='info'>
                CineZore - Hệ thống đặt vé xem phim trực tuyến<br>
                Địa chỉ: 123 Đường ABC, Quận 1, TP.HCM<br>
                Hotline: 1900 1234 | Email: <EMAIL><br>
                Website: <a href='https://cinezore.com'>cinezore.com</a>
            </div>
            <hr style='margin:15px 0;'>
            <p>Email này được gửi tự động từ hệ thống CineZore</p>
            <p>© {DateTime.Now.Year} CineZore</p>
        </div>
    </div>
</body>
</html>";
                    confirmMessage.Body = confirmBuilder.ToMessageBody();
                    using (var smtp2 = new SmtpClient())
                    {
                        await smtp2.ConnectAsync("smtp.gmail.com", 587, SecureSocketOptions.StartTls);
                        await smtp2.AuthenticateAsync("<EMAIL>", "mrta sixk cwnu egrq");
                        await smtp2.SendAsync(confirmMessage);
                        await smtp2.DisconnectAsync(true);
                    }
                    _logger.LogInformation($"Đã gửi email xác nhận đến khách: {email}");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Lỗi khi gửi email xác nhận cho khách: {ex.Message}");
                }

                // Kiểm tra nếu là yêu cầu AJAX
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = true, message = "Cảm ơn bạn đã gửi phản hồi!" });
                }

                TempData["SuccessMessage"] = "Cảm ơn bạn đã gửi phản hồi!";
                _logger.LogInformation("Hoàn thành gửi phản hồi thành công");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi gửi phản hồi: {ex.Message}");
                _logger.LogError($"Stack trace: {ex.StackTrace}");

                // Kiểm tra nếu là yêu cầu AJAX
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "Có lỗi xảy ra khi gửi phản hồi. Vui lòng thử lại sau." });
                }

                TempData["ErrorMessage"] = "Có lỗi xảy ra khi gửi phản hồi. Vui lòng thử lại sau.";
            }

            // Nếu không phải AJAX, chuyển hướng về trang chủ
            return RedirectToAction("Index");
        }









        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
