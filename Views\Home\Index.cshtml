﻿@model IEnumerable<CinemaBooking.Models.Phim>

@{
    ViewData["Title"] = "Trang chủ";
}

<style>
    /* Modern glassmorphism welcome message */
    .welcome-message-container {
        margin-top: 20px;
        margin-bottom: 30px;
    }

    .welcome-message {
        position: relative;
        padding: 25px 30px;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        animation: slideInDown 0.6s forwards;
    }

    .welcome-message:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }

    .admin-message {
        background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.3));
        border-left: 4px solid #3498db;
    }

    .user-message {
        background: linear-gradient(135deg, rgba(229, 9, 20, 0.2), rgba(192, 57, 43, 0.3));
        border-left: 4px solid #e50914;
    }

    .welcome-icon {
        font-size: 2.8rem;
        margin-right: 25px;
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
    }

    .welcome-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .welcome-message:hover .welcome-icon::before {
        transform: translateX(100%);
    }

    .admin-message .welcome-icon {
        color: #3498db;
        background: radial-gradient(circle, rgba(52, 152, 219, 0.2), rgba(52, 152, 219, 0.1));
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.4), inset 0 0 20px rgba(52, 152, 219, 0.1);
    }

    .user-message .welcome-icon {
        color: #e50914;
        background: radial-gradient(circle, rgba(229, 9, 20, 0.2), rgba(229, 9, 20, 0.1));
        box-shadow: 0 0 20px rgba(229, 9, 20, 0.4), inset 0 0 20px rgba(229, 9, 20, 0.1);
    }

    .welcome-content {
        flex: 1;
    }

    .welcome-content h3 {
        margin: 0 0 8px 0;
        font-size: 1.5rem;
        font-weight: 700;
        color: #fff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.5px;
    }

    .welcome-content p {
        margin: 0;
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.05rem;
        line-height: 1.6;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .welcome-close {
        position: absolute;
        top: 15px;
        right: 15px;
        background-color: transparent;
        color: rgba(255, 255, 255, 0.7);
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .welcome-close:hover {
        color: #fff;
        transform: rotate(90deg);
    }

    @@keyframes slideInDown {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    /* Modern movie container with enhanced styling */
    .movie-container {
        margin-bottom: 35px;
        padding: 0 12px;
        position: relative;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        z-index: 1;
    }

    .movie-container:hover {
        z-index: 10;
        transform: translateY(-8px);
    }

    .movie-card {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        cursor: pointer;
        height: 380px;
        overflow: hidden;
        border-radius: 16px;
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        width: 100%;
        transform-style: preserve-3d;
    }

    .movie-container::before {
        content: '';
        position: absolute;
        top: -40px;
        left: -40px;
        right: -40px;
        bottom: -40px;
        z-index: -1;
        pointer-events: none;
    }

    .movie-card:hover {
        transform: translateY(-12px) scale(1.02);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
    }

    .movie-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
        z-index: 1;
    }

    .movie-card:hover::before {
        transform: translateX(100%);
    }

    .card-img-top {
        height: 380px;
        width: 100%;
        object-fit: cover;
        transition: transform 0.4s ease;
        border-radius: 16px;
    }

    .movie-card:hover .card-img-top {
        transform: scale(1.05);
    }

    /* Enhanced movie card elements */
    .rating-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.9), rgba(255, 152, 0, 0.9));
        color: #fff;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 4px;
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        z-index: 4;
        transition: all 0.3s ease;
    }

    .rating-badge:hover {
        transform: scale(1.1);
    }

    .rating-badge.top-rated {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 193, 7, 0.9));
        animation: glow 2s ease-in-out infinite alternate;
    }

    @@keyframes glow {
        from {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 15px rgba(255, 215, 0, 0.5);
        }
        to {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 25px rgba(255, 215, 0, 0.8);
        }
    }

    .year-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        background: rgba(0, 0, 0, 0.8);
        color: #fff;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        backdrop-filter: blur(10px);
        z-index: 4;
    }

    .quick-actions {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        gap: 15px;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 5;
    }

    .movie-card:hover .quick-actions {
        opacity: 1;
    }

    .quick-btn {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: rgba(229, 9, 20, 0.9);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

    .quick-btn:hover {
        background: rgba(229, 9, 20, 1);
        transform: scale(1.1);
        color: #fff;
        box-shadow: 0 6px 20px rgba(229, 9, 20, 0.4);
    }

    .movie-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        flex-wrap: wrap;
        gap: 8px;
    }

    .movie-duration {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .release-date {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .card-actions {
        margin-top: 15px;
    }

    .movie-trailer-container {
        display: none;
    }

    .card-content {
        padding: 20px;
        background: linear-gradient(to top, rgba(0,0,0,0.95), rgba(0,0,0,0.7), rgba(0,0,0,0));
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        color: white;
        z-index: 3;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }

    .card-title {
        font-size: 1.1rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
        line-height: 1.3;
        letter-spacing: 0.3px;
    }

    .movie-genre {
        display: inline-block;
        background: linear-gradient(135deg, rgba(229, 9, 20, 0.9), rgba(192, 57, 43, 0.9));
        padding: 4px 12px;
        border-radius: 20px;
        margin-bottom: 8px;
        font-size: 0.75rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        box-shadow: 0 2px 8px rgba(229, 9, 20, 0.3);
    }

    .btn-detail {
        background: linear-gradient(135deg, #e50914, #c40812);
        border: none;
        margin-top: 10px;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        font-size: 0.85rem;
        padding: 8px 20px;
        border-radius: 25px;
        font-weight: 600;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
    }

    .btn-detail:hover {
        background: linear-gradient(135deg, #f40612, #e50914);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(229, 9, 20, 0.5);
    }

    /* Enhanced movie sections with glassmorphism */
    .movie-section {
        padding: 40px 0;
        margin-bottom: 40px;
    }

    .section-title {
        margin-bottom: 35px;
        font-weight: 700;
        position: relative;
        display: inline-block;
        font-size: 2.2rem;
        color: #fff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.5px;
    }

    .section-title::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -12px;
        height: 4px;
        width: 60px;
        background: linear-gradient(135deg, #e50914, #c40812);
        border-radius: 2px;
        box-shadow: 0 2px 8px rgba(229, 9, 20, 0.4);
    }

    .section-title::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: -8px;
        height: 2px;
        width: 100px;
        background: linear-gradient(135deg, rgba(229, 9, 20, 0.3), transparent);
        border-radius: 1px;
    }

    /* Enhanced section header styling */
    .section-header {
        margin-bottom: 35px;
    }

    .section-subtitle {
        margin-top: 15px;
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .section-subtitle .badge {
        font-size: 0.8rem;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        letter-spacing: 0.5px;
        animation: pulse 2s ease-in-out infinite;
    }

    @@keyframes pulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
        }
    }

    /* No movies message styling */
    .no-movies-message {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 40px 30px;
        margin: 20px 0;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }

    .no-movies-icon {
        animation: float 3s ease-in-out infinite;
    }

    .quality-criteria {
        display: flex;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        margin-top: 20px;
    }

    .criteria-item {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }

    .criteria-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.15);
    }

    .movie-section {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 40px 30px;
        margin-bottom: 40px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        margin-top: 30px;
        color: #fff;
        position: relative;
        overflow: hidden;
    }

    .movie-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        z-index: -1;
    }

    .hero-section {
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 20px;
        overflow: hidden;
        margin: 0;
        padding: 0;
        position: relative;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.1);
        min-height: 600px;
    }

    /* Enhanced hero styles */
    .hero-logo {
        transition: all 0.4s ease;
        animation: float 3s ease-in-out infinite;
    }

    .hero-logo:hover {
        transform: scale(1.05);
        filter: drop-shadow(0 0 30px rgba(229, 9, 20, 0.7)) !important;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 25px;
        line-height: 1.2;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
    }

    .gradient-text {
        background: linear-gradient(135deg, #fff, #e50914);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: shimmer 3s ease-in-out infinite;
    }

    .highlight-text {
        color: #e50914;
        text-shadow: 0 0 20px rgba(229, 9, 20, 0.5);
    }

    @@keyframes shimmer {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .hero-subtitle {
        font-size: 1.3rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 40px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
    }

    .hero-stats {
        display: flex;
        justify-content: center;
        gap: 60px;
        margin-bottom: 40px;
        flex-wrap: wrap;
    }

    .stat-item {
        text-align: center;
        padding: 20px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        min-width: 120px;
    }

    .stat-item:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        color: #e50914;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
        text-transform: uppercase;
        letter-spacing: 1px;
        font-weight: 600;
    }

    .hero-actions {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .btn-primary-gradient {
        background: linear-gradient(135deg, #e50914, #c40812);
        border: none;
        padding: 15px 35px;
        font-weight: 700;
        letter-spacing: 0.5px;
        border-radius: 50px;
        box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        text-transform: uppercase;
        position: relative;
        overflow: hidden;
    }

    .btn-primary-gradient:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(229, 9, 20, 0.6);
        background: linear-gradient(135deg, #f40612, #e50914);
    }

    .btn-outline-gradient {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.8);
        color: #fff;
        padding: 13px 33px;
        font-weight: 700;
        letter-spacing: 0.5px;
        border-radius: 50px;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        text-transform: uppercase;
        backdrop-filter: blur(10px);
    }

    .btn-outline-gradient:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: #e50914;
        color: #e50914;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    /* Hero overlay and particles */
    .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(0,0,0,0.6), rgba(229, 9, 20, 0.1));
        z-index: 1;
        pointer-events: none;
    }

    .particles {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(229, 9, 20, 0.6);
        border-radius: 50%;
        animation: float-particle 6s infinite linear;
    }

    .particle:nth-child(1) {
        left: 20%;
        animation-delay: 0s;
        animation-duration: 6s;
    }

    .particle:nth-child(2) {
        left: 40%;
        animation-delay: 1s;
        animation-duration: 8s;
    }

    .particle:nth-child(3) {
        left: 60%;
        animation-delay: 2s;
        animation-duration: 7s;
    }

    .particle:nth-child(4) {
        left: 80%;
        animation-delay: 3s;
        animation-duration: 9s;
    }

    .particle:nth-child(5) {
        left: 90%;
        animation-delay: 4s;
        animation-duration: 5s;
    }

    @@keyframes float-particle {
        0% {
            transform: translateY(100vh) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100px) rotate(360deg);
            opacity: 0;
        }
    }

    @@keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    /* Responsive hero styles */
    @@media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .hero-stats {
            gap: 30px;
        }

        .stat-item {
            min-width: 100px;
            padding: 15px;
        }

        .stat-number {
            font-size: 2rem;
        }

        .hero-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-primary-gradient,
        .btn-outline-gradient {
            width: 100%;
            max-width: 300px;
        }
    }

    /* Featured Movies Section */
    .featured-section {
        background: rgba(255, 255, 255, 0.03);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.08);
        border-radius: 20px;
        padding: 50px 30px;
        margin-bottom: 50px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        margin-top: 40px;
        color: #fff;
        position: relative;
        overflow: hidden;
    }

    .featured-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(229, 9, 20, 0.05), rgba(255, 255, 255, 0.02));
        z-index: -1;
    }

    .featured-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .featured-card {
        background: rgba(255, 255, 255, 0.08);
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        cursor: pointer;
    }

    .featured-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        border-color: rgba(229, 9, 20, 0.3);
    }

    .featured-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .featured-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.4s ease;
    }

    .featured-card:hover .featured-image img {
        transform: scale(1.1);
    }

    .featured-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: linear-gradient(135deg, #e50914, #c40812);
        color: #fff;
        padding: 8px 15px;
        border-radius: 25px;
        font-size: 0.8rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 5px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.4);
        z-index: 3;
        animation: pulse-featured 2s ease-in-out infinite;
    }

    @@keyframes pulse-featured {
        0%, 100% {
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.4);
        }
        50% {
            box-shadow: 0 4px 25px rgba(229, 9, 20, 0.7);
        }
    }

    .play-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 2;
    }

    .featured-card:hover .play-overlay {
        opacity: 1;
    }

    .play-btn {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e50914, #c40812);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        font-size: 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
    }

    .play-btn:hover {
        transform: scale(1.1);
        color: #fff;
        box-shadow: 0 12px 35px rgba(229, 9, 20, 0.6);
    }

    .featured-content {
        padding: 25px;
    }

    .featured-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 15px;
        color: #fff;
        line-height: 1.3;
    }

    .featured-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .featured-genre {
        background: linear-gradient(135deg, rgba(229, 9, 20, 0.8), rgba(192, 57, 43, 0.8));
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .featured-rating {
        color: #ffc107;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .featured-description {
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.6;
        margin-bottom: 20px;
        font-size: 0.95rem;
    }

    .featured-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }

    .btn-featured {
        background: linear-gradient(135deg, #e50914, #c40812);
        border: none;
        color: #fff;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
    }

    .btn-featured:hover {
        background: linear-gradient(135deg, #f40612, #e50914);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(229, 9, 20, 0.5);
        color: #fff;
    }

    .btn-featured-outline {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: #fff;
        padding: 8px 18px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        backdrop-filter: blur(10px);
    }

    .btn-featured-outline:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: #e50914;
        color: #e50914;
        transform: translateY(-2px);
    }

    /* Responsive featured section */
    @@media (max-width: 768px) {
        .featured-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .featured-image {
            height: 180px;
        }

        .featured-actions {
            flex-direction: column;
        }

        .btn-featured,
        .btn-featured-outline {
            justify-content: center;
            width: 100%;
        }
    }

    /* Carousel styling */
    .carousel-control-prev, .carousel-control-next {
        width: 5%;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        height: 50px;
        width: 50px;
        top: 50%;
        transform: translateY(-50%);
    }

    .carousel-control-prev {
        left: -25px;
    }

    .carousel-control-next {
        right: -25px;
    }

    .carousel-item {
        padding: 10px 20px;
    }

    .carousel-inner {
        overflow: visible;
    }

    /* CSS cho carousel kiểu mới */
    .movie-carousel-container {
        position: relative;
        overflow: hidden;
        padding: 0 50px;
    }

    .movie-carousel-wrapper {
        overflow: hidden;
        margin: 0 auto;
        width: 100%;
    }

    .movie-carousel-track {
        display: flex;
        transition: transform 0.5s ease;
        will-change: transform;
        backface-visibility: hidden;
    }

    .movie-slide {
        flex: 0 0 25%; /* Hiển thị 4 phim mỗi lần */
        max-width: 25%;
        padding: 0 10px;
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .movie-slide.clone {
        /* Style cho các slide clone nếu cần */
    }

    /* Hiệu ứng cho slide đang chuyển đổi */
    .movie-carousel-track.transitioning .movie-slide {
        transition: transform 0.5s ease, opacity 0.5s ease;
    }

    .carousel-control-prev, .carousel-control-next {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        border: none;
        transition: all 0.3s ease;
    }

    .carousel-control-prev:hover, .carousel-control-next:hover {
        background-color: rgba(229, 9, 20, 0.8);
    }

    .carousel-control-prev {
        left: 0;
    }

    .carousel-control-next {
        right: 0;
    }

    /* Responsive */
    @@media (max-width: 992px) {
        .movie-slide {
            flex: 0 0 33.333%; /* Hiển thị 3 phim trên tablet */
            max-width: 33.333%;
        }
    }

    @@media (max-width: 768px) {
        .movie-slide {
            flex: 0 0 50%; /* Hiển thị 2 phim trên mobile lớn */
            max-width: 50%;
        }
    }

    @@media (max-width: 576px) {
        .movie-slide {
            flex: 0 0 100%; /* Hiển thị 1 phim trên mobile nhỏ */
            max-width: 100%;
        }
    }

    /* App Introduction Section Styles */
    .app-intro-section {
        background: rgba(255, 255, 255, 0.03);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.08);
        border-radius: 25px;
        padding: 60px 40px;
        margin: 50px 0;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
        color: #fff;
        position: relative;
        overflow: hidden;
    }

    .app-intro-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(229, 9, 20, 0.08), rgba(255, 255, 255, 0.02));
        z-index: -1;
    }

    .app-intro-title {
        font-size: 2.8rem;
        font-weight: 800;
        margin-bottom: 25px;
        text-align: center;
        background: linear-gradient(135deg, #fff, #e50914);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        line-height: 1.2;
    }

    .app-intro-subtitle {
        font-size: 1.3rem;
        color: rgba(255, 255, 255, 0.9);
        text-align: center;
        margin-bottom: 50px;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 50px;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.08);
        border-radius: 20px;
        padding: 35px 25px;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .feature-card:hover::before {
        transform: translateX(100%);
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        border-color: rgba(229, 9, 20, 0.3);
        background: rgba(255, 255, 255, 0.12);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 25px;
        background: linear-gradient(135deg, rgba(229, 9, 20, 0.2), rgba(229, 9, 20, 0.1));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.2rem;
        color: #e50914;
        box-shadow: 0 0 30px rgba(229, 9, 20, 0.3), inset 0 0 30px rgba(229, 9, 20, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .feature-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .feature-card:hover .feature-icon::before {
        transform: translateX(100%);
    }

    .feature-card:hover .feature-icon {
        transform: scale(1.1);
        box-shadow: 0 0 40px rgba(229, 9, 20, 0.5), inset 0 0 40px rgba(229, 9, 20, 0.15);
    }

    .feature-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 15px;
        color: #fff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .feature-description {
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.6;
        font-size: 1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .app-download-section {
        text-align: center;
        padding: 40px 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .download-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: #fff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .download-subtitle {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 30px;
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .download-buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .download-btn {
        background: linear-gradient(135deg, #e50914, #c40812);
        border: none;
        color: #fff;
        padding: 15px 30px;
        border-radius: 50px;
        font-weight: 700;
        font-size: 1rem;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;
    }

    .download-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .download-btn:hover::before {
        transform: translateX(100%);
    }

    .download-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(229, 9, 20, 0.6);
        background: linear-gradient(135deg, #f40612, #e50914);
        color: #fff;
    }

    .download-btn i {
        font-size: 1.2rem;
    }

    /* Responsive styles for app intro */
    @@media (max-width: 768px) {
        .app-intro-section {
            padding: 40px 25px;
            margin: 30px 0;
        }

        .app-intro-title {
            font-size: 2.2rem;
        }

        .app-intro-subtitle {
            font-size: 1.1rem;
        }

        .features-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .feature-card {
            padding: 25px 20px;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            font-size: 1.8rem;
        }

        .download-buttons {
            flex-direction: column;
            align-items: center;
        }

        .download-btn {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }
</style>

@if (User.Identity.IsAuthenticated)
{
    <div class="container mb-4 welcome-message-container reveal">
        <div class="welcome-message @(User.IsInRole("Admin") ? "admin-message" : "user-message")">
            <div class="welcome-icon">
                <i class="@(User.IsInRole("Admin") ? "fas fa-user-shield" : "fas fa-user")"></i>
            </div>
            <div class="welcome-content">
                @if (User.IsInRole("Admin"))
                {
                    <h3>Xin chào Quản trị viên!</h3>
                    <p>Chào mừng bạn quay trở lại hệ thống. Chúc bạn một ngày làm việc hiệu quả và thuận lợi trong việc quản lý!</p>
                }
                else
                {
                    <h3>Xin chào bạn!</h3>
                    <p>Rất vui được gặp lại bạn. Chúc bạn có trải nghiệm tuyệt vời khi sử dụng dịch vụ của chúng tôi!</p>
                }
            </div>
            <button class="btn-close welcome-close" aria-label="Close"></button>
        </div>
    </div>
}

<div class="container mb-5">
    <div class="hero-section parallax" data-speed="0.2">
        <div style="position: relative; z-index: 10; padding: 80px 0;">
            <div class="text-center">
                <!-- Enhanced logo with glow effect -->
                <div class="logo-container reveal reveal-delay-1" style="margin-bottom: 30px;">
                    <img src="~/images/imagescinezore-logo.png" alt="CineZore" class="hero-logo" style="max-width: 280px; filter: drop-shadow(0 0 20px rgba(229, 9, 20, 0.5));">
                </div>

                <!-- Enhanced title with gradient text -->
                <h1 class="hero-title reveal reveal-delay-2">
                    <span class="gradient-text">Trải nghiệm điện ảnh</span><br>
                    <span class="highlight-text">tuyệt vời nhất</span>
                </h1>

                <!-- Enhanced subtitle -->
                <p class="hero-subtitle reveal reveal-delay-3">
                    Khám phá thế giới phim ảnh với chất lượng hình ảnh và âm thanh đỉnh cao
                </p>

                <!-- Enhanced statistics -->
                <div class="hero-stats reveal reveal-delay-4">
                    <div class="stat-item">
                        <div class="stat-number">@ViewBag.TotalMovies+</div>
                        <div class="stat-label">Phim hot</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">@ViewBag.TotalCinemas+</div>
                        <div class="stat-label">Rạp chiếu</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">@ViewBag.TotalBookings+</div>
                        <div class="stat-label">Đặt vé</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Hỗ trợ</div>
                    </div>
                </div>

                <!-- Enhanced action buttons -->
                <div class="hero-actions reveal reveal-delay-5">
                    <a href="#movie-list" class="btn btn-primary-gradient btn-lg btn-pulse">
                        <i class="fas fa-play me-2"></i>
                        ĐẶT VÉ NGAY
                    </a>
                    <a asp-controller="Search" asp-action="MoviesByDate" asp-route-date="@DateTime.Now.ToString("yyyy-MM-dd")" class="btn btn-outline-gradient btn-lg">
                        <i class="fas fa-calendar-day me-2"></i>
                        LỊCH CHIẾU HÔM NAY
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced overlay with animated particles -->
        <div class="hero-overlay">
            <div class="particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>
        </div>
    </div>
</div>

<!-- Featured Movies Section -->
@if (ViewBag.FeaturedMovies != null && ((IEnumerable<CinemaBooking.Models.Phim>)ViewBag.FeaturedMovies).Count() > 0)
{
    <div class="container featured-section reveal">
        <h1 class="section-title reveal reveal-delay-1">
            <i class="fas fa-fire me-3"></i>Phim nổi bật tuần này
        </h1>

        <div class="featured-grid reveal reveal-delay-2">
            @foreach (var item in (IEnumerable<CinemaBooking.Models.Phim>)ViewBag.FeaturedMovies)
            {
                <div class="featured-card" data-id="@item.MaPhim">
                    <div class="featured-image">
                        <img src="@(string.IsNullOrEmpty(item.UrlPoster) ? "/images/no-image.jpg" : item.UrlPoster)" alt="@item.TenPhim">

                        <!-- Featured badge -->
                        <div class="featured-badge">
                            <i class="fas fa-star"></i>
                            Nổi bật
                        </div>

                        <!-- Play button overlay -->
                        <div class="play-overlay">
                            <a asp-controller="Phim" asp-action="Detail" asp-route-id="@item.MaPhim" class="play-btn">
                                <i class="fas fa-play"></i>
                            </a>
                        </div>
                    </div>

                    <div class="featured-content">
                        <h3 class="featured-title">@item.TenPhim</h3>
                        <div class="featured-meta">
                            <span class="featured-genre">@item.TheLoai</span>
                            <span class="featured-rating">
                                <i class="fas fa-star"></i>
                                @{
                                    double avgRating2 = 0;
                                    if (item.DanhGias != null && item.DanhGias.Count > 0)
                                    {
                                        var validRatings2 = new List<double>();
                                        foreach (var rating in item.DanhGias)
                                        {
                                            if (rating.DiemSo != null && rating.DiemSo > 0)
                                                validRatings2.Add((double)rating.DiemSo);
                                        }
                                        if (validRatings2.Count > 0)
                                            avgRating2 = validRatings2.Average();
                                    }
                                }@Math.Round(avgRating2, 1)
                            </span>
                        </div>
                        <p class="featured-description">@(item.MoTa?.Length > 100 ? item.MoTa.Substring(0, 100) + "..." : item.MoTa)</p>

                        <div class="featured-actions">
                            <a asp-controller="Phim" asp-action="Detail" asp-route-id="@item.MaPhim" class="btn btn-featured">
                                <i class="fas fa-info-circle me-2"></i>Chi tiết
                            </a>
                            <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@item.MaPhim" class="btn btn-featured-outline">
                                <i class="fas fa-ticket-alt me-2"></i>Đặt vé
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}

<div id="movie-list" class="container movie-section reveal">
    <div class="section-header reveal reveal-delay-1">
        <h1 class="section-title">Phim mới nhất</h1>
        <div class="section-subtitle">
            <i class="fas fa-film text-primary me-2"></i>
            Hiển thị tối đa 25 bộ phim mới nhất
            <span class="badge bg-primary ms-2">
                <i class="fas fa-calendar-alt me-1"></i>Cập nhật liên tục
            </span>
        </div>
    </div>

    <!-- Grid layout 5x5 -->
    <div class="movies-grid-container reveal reveal-delay-2">
        @if (Model != null && Model.Any())
        {
            <div class="movies-grid">
                @foreach (var item in Model.Take(25))
                {
                    <div class="movie-grid-item">
                        <div class="movie-card" data-id="@item.MaPhim">
                            <!-- Movie poster with overlay -->
                            <div class="img-hover-zoom">
                                <img src="@(string.IsNullOrEmpty(item.UrlPoster) ? "/images/no-image.jpg" : item.UrlPoster)" class="card-img-top" alt="@item.TenPhim">

                                <!-- Rating badge -->
                                <div class="rating-badge">
                                    <i class="fas fa-star"></i>
                                    <span>@{
                                        double avgRating1 = 0;
                                        if (item.DanhGias != null && item.DanhGias.Count > 0)
                                        {
                                            var validRatings1 = new List<double>();
                                            foreach (var rating in item.DanhGias)
                                            {
                                                if (rating.DiemSo != null && rating.DiemSo > 0)
                                                    validRatings1.Add((double)rating.DiemSo);
                                            }
                                            if (validRatings1.Count > 0)
                                                avgRating1 = validRatings1.Average();
                                        }
                                    }@Math.Round(avgRating1, 1)</span>
                                </div>

                                <!-- New badge for latest movies -->
                                <div class="new-badge">
                                    <i class="fas fa-sparkles"></i>
                                    <span>MỚI</span>
                                </div>

                                <!-- Quick action overlay -->
                                <div class="quick-actions">
                                    <a asp-controller="Phim" asp-action="Detail" asp-route-id="@item.MaPhim" class="quick-btn" title="Xem chi tiết">
                                        <i class="fas fa-info-circle"></i>
                                    </a>
                                    <a href="#" class="quick-btn" title="Thêm vào yêu thích">
                                        <i class="fas fa-heart"></i>
                                    </a>
                                    <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@item.MaPhim" class="quick-btn" title="Đặt vé ngay">
                                        <i class="fas fa-ticket-alt"></i>
                                    </a>
                                </div>
                            </div>

                            <!-- Enhanced card content -->
                            <div class="card-content">
                                <h5 class="card-title">@item.TenPhim</h5>

                                <div class="movie-meta">
                                    <span class="movie-genre">@item.TheLoai</span>
                                    <span class="movie-duration">
                                        <i class="fas fa-clock"></i>
                                        @item.ThoiLuong phút
                                    </span>
                                </div>

                                <div class="card-actions">
                                    <a asp-controller="Phim" asp-action="Detail" asp-route-id="@item.MaPhim" class="btn btn-detail btn-ripple">
                                        <i class="fas fa-play me-1"></i>
                                        Chi tiết
                                    </a>
                                    <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@item.MaPhim" class="btn btn-primary btn-ripple">
                                        <i class="fas fa-ticket-alt me-1"></i>
                                        Đặt vé
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="no-movies-message text-center py-5">
                <div class="no-movies-icon mb-4">
                    <i class="fas fa-film" style="font-size: 4rem; color: rgba(0, 123, 255, 0.6);"></i>
                </div>
                <h4 class="text-white mb-3">Chưa có phim mới nào</h4>
                <p class="text-muted mb-4">
                    Hiện tại chưa có phim mới nào được thêm vào hệ thống.
                    <br>Hãy quay lại sau để khám phá những bộ phim mới nhất!
                </p>
            </div>
        }
    </div>

    <!-- View all button -->
    @if (Model != null && Model.Count() > 25)
    {
        <div class="text-center mt-4 reveal reveal-delay-3">
            <a asp-controller="Search" asp-action="Index" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-th-large me-2"></i>
                Xem tất cả phim (@Model.Count() phim)
            </a>
        </div>
    }
</div>

<!-- Phim được đánh giá cao -->
<div id="top-rated-movies" class="container movie-section reveal mt-5">
    <div class="section-header reveal reveal-delay-1">
        <h1 class="section-title">Phim được đánh giá cao</h1>
        <div class="section-subtitle">
            <i class="fas fa-star text-warning me-2"></i>
            Chỉ những phim có điểm đánh giá từ 4.0 sao trở lên
            <span class="badge bg-warning text-dark ms-2">
                <i class="fas fa-crown me-1"></i>Chất lượng cao
            </span>
        </div>
    </div>

    <div class="movie-carousel-container position-relative reveal reveal-delay-2">
        <!-- Nút điều hướng trái -->
        <button class="carousel-control-prev" id="prevBtnRated" type="button">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
        </button>

        <!-- Vùng hiển thị phim -->
        <div class="movie-carousel-wrapper">
            @if (ViewBag.TopRatedMovies != null && ((IEnumerable<CinemaBooking.Models.Phim>)ViewBag.TopRatedMovies).Any())
            {
                <div class="movie-carousel-track" id="topRatedTrack">
                    @foreach (var item in ViewBag.TopRatedMovies)
                    {
                    <div class="movie-slide">
                        <div class="movie-container">
                            <div class="movie-card" data-id="@item.MaPhim">
                                <!-- Movie poster with overlay -->
                                <div class="img-hover-zoom">
                                    <img src="@(string.IsNullOrEmpty(item.UrlPoster) ? "/images/no-image.jpg" : item.UrlPoster)" class="card-img-top" alt="@item.TenPhim">

                                    <!-- Rating badge with top-rated indicator -->
                                    <div class="rating-badge top-rated">
                                        <i class="fas fa-crown"></i>
                                        <span>@{
                                            double avgRating = 0;
                                            if (item.DanhGias != null && item.DanhGias.Count > 0)
                                            {
                                                var validRatings = new List<double>();
                                                foreach (var rating in item.DanhGias)
                                                {
                                                    if (rating.DiemSo != null && rating.DiemSo > 0)
                                                        validRatings.Add((double)rating.DiemSo);
                                                }
                                                if (validRatings.Count > 0)
                                                    avgRating = validRatings.Average();
                                            }
                                        }@Math.Round(avgRating, 1)</span>
                                    </div>

                                    <!-- Release year badge -->
                                    <!-- Temporarily disabled due to runtime binding issue -->

                                    <!-- Quick action overlay -->
                                    <div class="quick-actions">
                                        <a asp-controller="Phim" asp-action="Detail" asp-route-id="@item.MaPhim" class="quick-btn" title="Xem chi tiết">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a href="#" class="quick-btn" title="Thêm vào yêu thích">
                                            <i class="fas fa-heart"></i>
                                        </a>
                                        <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@item.MaPhim" class="quick-btn" title="Đặt vé ngay">
                                            <i class="fas fa-ticket-alt"></i>
                                        </a>
                                    </div>
                                </div>

                                <!-- Enhanced card content -->
                                <div class="card-content">
                                    <h5 class="card-title">@item.TenPhim</h5>

                                    <div class="movie-meta">
                                        <span class="movie-genre">@item.TheLoai</span>
                                        <span class="movie-duration">
                                            <i class="fas fa-clock"></i>
                                            @item.ThoiLuong phút
                                        </span>
                                    </div>

                                    <!-- Temporarily disabled due to runtime binding issue -->

                                    <div class="card-actions">
                                        <a asp-controller="Phim" asp-action="Detail" asp-route-id="@item.MaPhim" class="btn btn-detail btn-ripple">
                                            <i class="fas fa-play me-1"></i>
                                            Chi tiết
                                        </a>
                                        <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@item.MaPhim" class="btn btn-primary btn-ripple">
                                            <i class="fas fa-ticket-alt me-1"></i>
                                            Đặt vé
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                </div>
            }
            else
            {
                <div class="no-movies-message text-center py-5">
                    <div class="no-movies-icon mb-4">
                        <i class="fas fa-star-half-alt" style="font-size: 4rem; color: rgba(255, 193, 7, 0.6);"></i>
                    </div>
                    <h4 class="text-white mb-3">Chưa có phim nào đạt tiêu chuẩn</h4>
                    <p class="text-muted mb-4">
                        Hiện tại chưa có phim nào có điểm đánh giá từ 4.0 sao trở lên.
                        <br>Hãy quay lại sau để khám phá những bộ phim chất lượng cao!
                    </p>
                    <div class="quality-criteria">
                        <div class="criteria-item">
                            <i class="fas fa-star text-warning me-2"></i>
                            <span>Điểm đánh giá ≥ 4.0/5.0</span>
                        </div>
                        <div class="criteria-item">
                            <i class="fas fa-users text-info me-2"></i>
                            <span>Tối thiểu 1 lượt đánh giá</span>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Nút điều hướng phải -->
        <button class="carousel-control-next" id="nextBtnRated" type="button">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
        </button>
    </div>
</div>

<!-- App Introduction Section -->
<div class="container app-intro-section reveal">
    <h2 class="app-intro-title reveal reveal-delay-1">
        <i class="fas fa-mobile-alt me-3"></i>
        Ứng dụng đặt vé xem phim CineZore
    </h2>

    <p class="app-intro-subtitle reveal reveal-delay-2">
        Trải nghiệm đặt vé xem phim hoàn toàn mới với ứng dụng CineZore.
        Dễ dàng, nhanh chóng và tiện lợi ngay trên điện thoại của bạn.
    </p>

    <div class="features-grid reveal reveal-delay-3">
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <h3 class="feature-title">Đặt vé dễ dàng</h3>
            <p class="feature-description">
                Chọn phim, chọn suất chiếu, chọn ghế và thanh toán chỉ trong vài thao tác đơn giản.
                Giao diện thân thiện, dễ sử dụng cho mọi lứa tuổi.
            </p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="feature-title">Tiết kiệm thời gian</h3>
            <p class="feature-description">
                Không cần xếp hàng mua vé tại rạp. Đặt vé online 24/7,
                kiểm tra lịch chiếu và nhận thông báo về các bộ phim mới nhất.
            </p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-credit-card"></i>
            </div>
            <h3 class="feature-title">Thanh toán an toàn</h3>
            <p class="feature-description">
                Hỗ trợ nhiều phương thức thanh toán: thẻ tín dụng, ví điện tử,
                chuyển khoản ngân hàng. Bảo mật thông tin tuyệt đối.
            </p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-star"></i>
            </div>
            <h3 class="feature-title">Đánh giá & Review</h3>
            <p class="feature-description">
                Xem đánh giá từ cộng đồng, chia sẻ cảm nhận về bộ phim yêu thích.
                Khám phá những bộ phim được đánh giá cao nhất.
            </p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-gift"></i>
            </div>
            <h3 class="feature-title">Ưu đãi độc quyền</h3>
            <p class="feature-description">
                Nhận thông báo về các chương trình khuyến mãi, giảm giá đặc biệt
                và tích điểm để đổi quà hấp dẫn.
            </p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-history"></i>
            </div>
            <h3 class="feature-title">Lịch sử đặt vé</h3>
            <p class="feature-description">
                Theo dõi lịch sử đặt vé, quản lý vé đã mua và dễ dàng đặt lại
                những bộ phim yêu thích.
            </p>
        </div>
    </div>

    <div class="app-download-section reveal reveal-delay-4">
        <h3 class="download-title">
            <i class="fas fa-download me-2"></i>
            Tải ứng dụng ngay hôm nay
        </h3>
        <p class="download-subtitle">
            Có sẵn trên tất cả các nền tảng. Tải về miễn phí và trải nghiệm ngay!
        </p>
        <div class="download-buttons">
            <a href="#" class="download-btn">
                <i class="fab fa-apple"></i>
                App Store
            </a>
            <a href="#" class="download-btn">
                <i class="fab fa-google-play"></i>
                Google Play
            </a>
            <a href="#" class="download-btn">
                <i class="fas fa-globe"></i>
                Web App
            </a>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Xử lý đóng thông báo lời chào
        const welcomeCloseBtn = document.querySelector('.welcome-close');
        const welcomeMessage = document.querySelector('.welcome-message');

        if (welcomeCloseBtn && welcomeMessage) {
            welcomeCloseBtn.addEventListener('click', function() {
                // Thêm hiệu ứng fade out
                welcomeMessage.style.opacity = '0';
                welcomeMessage.style.transform = 'translateY(-20px)';

                // Sau khi hiệu ứng hoàn thành, ẩn phần tử
                setTimeout(function() {
                    welcomeMessage.parentElement.style.display = 'none';
                }, 300);

                // Lưu trạng thái đã đóng vào localStorage
                localStorage.setItem('welcomeMessageClosed', 'true');
            });

            // Kiểm tra xem thông báo đã được đóng trước đó chưa
            if (localStorage.getItem('welcomeMessageClosed') === 'true') {
                welcomeMessage.parentElement.style.display = 'none';
            }
        }

        // Hàm khởi tạo carousel
        function initializeCarousel(trackSelector, prevBtnSelector, nextBtnSelector) {
            const track = document.querySelector(trackSelector);
            if (!track) return; // Nếu không tìm thấy track, thoát khỏi hàm

            const slides = track.querySelectorAll('.movie-slide');
            if (slides.length === 0) return; // Nếu không có slide nào, thoát khỏi hàm

            const prevButton = document.getElementById(prevBtnSelector);
            const nextButton = document.getElementById(nextBtnSelector);

            // Phim hiển thị mỗi lần (tùy theo kích thước màn hình)
            const slidesToShow = window.innerWidth < 768
                ? (window.innerWidth < 576 ? 1 : 2)
                : (window.innerWidth < 992 ? 3 : 4);

            const totalSlides = slides.length;
            let slideWidth = 100 / slidesToShow; // Chiều rộng mỗi slide (%)
            let currentIndex = 0;
            let isTransitioning = false;

            // Clone các slide để tạo hiệu ứng vô hạn
            function setupInfiniteCarousel() {
                // Xóa các clone cũ nếu có
                track.querySelectorAll('.movie-slide.clone').forEach(clone => clone.remove());

                // Clone các slide đầu để thêm vào cuối
                for (let i = 0; i < Math.min(slidesToShow, totalSlides); i++) {
                    const clone = slides[i].cloneNode(true);
                    clone.classList.add('clone');
                    track.appendChild(clone);
                }

                // Clone các slide cuối để thêm vào đầu
                for (let i = Math.max(0, totalSlides - 1); i >= Math.max(0, totalSlides - slidesToShow); i--) {
                    const clone = slides[i].cloneNode(true);
                    clone.classList.add('clone');
                    track.insertBefore(clone, track.firstChild);
                }

                // Thiết lập vị trí ban đầu
                resetPosition();
            }

            // Đặt lại vị trí carousel về ban đầu (không animation)
            function resetPosition() {
                // Tắt transition
                track.style.transition = 'none';

                // Tính toán vị trí cần reset
                const adjustedIndex = ((currentIndex % totalSlides) + totalSlides) % totalSlides;
                const offsetPosition = (slidesToShow + adjustedIndex) * slideWidth;

                // Đặt lại vị trí
                track.style.transform = `translateX(-${offsetPosition}%)`;

                // Force reflow để đảm bảo transition bị vô hiệu hóa
                track.offsetHeight;

                // Bật lại transition
                track.style.transition = 'transform 0.5s ease';
            }

            // Cập nhật vị trí carousel với hiệu ứng mượt
            function updateCarouselPosition(instant = false) {
                // Tính toán vị trí mới (với offset từ các slide clone)
                const offsetPosition = (slidesToShow + currentIndex) * slideWidth;

                // Thêm hoặc bỏ transition tùy vào tham số instant
                if (instant) {
                    track.style.transition = 'none';
                    track.classList.remove('transitioning');
                } else {
                    track.style.transition = 'transform 0.5s ease';
                    track.classList.add('transitioning');
                    isTransitioning = true;
                }

                // Di chuyển đến vị trí mới
                track.style.transform = `translateX(-${offsetPosition}%)`;

                // Xử lý kết thúc transition
                if (!instant) {
                    track.addEventListener('transitionend', handleTransitionEnd, { once: true });
                }
            }

            // Xử lý sự kiện khi transition kết thúc (để xử lý infinite scroll)
            function handleTransitionEnd() {
                isTransitioning = false;
                track.classList.remove('transitioning');

                // Reset vị trí nếu đã đi quá giới hạn
                if (currentIndex >= totalSlides) {
                    currentIndex = currentIndex % totalSlides;
                    resetPosition();
                    updateCarouselPosition(true);
                } else if (currentIndex < 0) {
                    currentIndex = totalSlides + (currentIndex % totalSlides);
                    resetPosition();
                    updateCarouselPosition(true);
                }
            }

            // Xử lý nút Next
            if (nextButton) {
                nextButton.addEventListener('click', () => {
                    if (isTransitioning) return;
                    currentIndex++;
                    updateCarouselPosition();
                });
            }

            // Xử lý nút Prev
            if (prevButton) {
                prevButton.addEventListener('click', () => {
                    if (isTransitioning) return;
                    currentIndex--;
                    updateCarouselPosition();
                });
            }

            // Thêm xử lý touch swipe cho mobile
            let touchStartX = 0;
            let touchEndX = 0;

            const carouselWrapper = track.closest('.movie-carousel-wrapper');
            if (carouselWrapper) {
                carouselWrapper.addEventListener('touchstart', (e) => {
                    touchStartX = e.changedTouches[0].screenX;
                }, { passive: true });

                carouselWrapper.addEventListener('touchend', (e) => {
                    touchEndX = e.changedTouches[0].screenX;
                    handleSwipe();
                }, { passive: true });
            }

            function handleSwipe() {
                const swipeThreshold = 50; // Ngưỡng để xác định swipe

                if (touchEndX < touchStartX - swipeThreshold) {
                    // Swipe trái -> Next slide
                    if (!isTransitioning) {
                        currentIndex++;
                        updateCarouselPosition();
                    }
                }

                if (touchEndX > touchStartX + swipeThreshold) {
                    // Swipe phải -> Previous slide
                    if (!isTransitioning) {
                        currentIndex--;
                        updateCarouselPosition();
                    }
                }
            }

            // Khởi tạo carousel
            setupInfiniteCarousel();

            // Khởi tạo vị trí ban đầu
            updateCarouselPosition(true);
        }

        // Chỉ khởi tạo carousel cho phim được đánh giá cao (phim mới nhất dùng grid)
        initializeCarousel('#topRatedTrack', 'prevBtnRated', 'nextBtnRated');

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href');

                // Bỏ qua nếu href chỉ là "#" hoặc rỗng
                if (!targetId || targetId === '#' || targetId.length <= 1) {
                    return;
                }

                e.preventDefault();
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 70, // Offset for navbar
                        behavior: 'smooth'
                    });
                }
            });
        });
    });
</script>