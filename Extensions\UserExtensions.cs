using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using CinemaBooking.Models;
using CinemaBooking.Data;
using Microsoft.EntityFrameworkCore;

namespace CinemaBooking.Extensions
{
    public static class UserExtensions
    {
        /// <summary>
        /// Gets the legacy user ID (MaNguoiDung) for the current user.
        /// This method handles both Identity users and legacy users.
        /// </summary>
        public static async Task<int?> GetLegacyUserIdAsync(this ClaimsPrincipal user, ApplicationDbContext context)
        {
            if (!user.Identity.IsAuthenticated)
                return null;

            // First try to get from legacy claim (for backward compatibility)
            var legacyUserIdClaim = user.FindFirst("MaNguoiDung")?.Value;
            if (!string.IsNullOrEmpty(legacyUserIdClaim) && int.TryParse(legacyUserIdClaim, out int legacyUserId))
            {
                return legacyUserId;
            }

            // If not found, try to get from Identity system
            var identityUserId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!string.IsNullOrEmpty(identityUserId))
            {
                // Find the corresponding legacy user by email
                var email = user.FindFirst(ClaimTypes.Email)?.Value;
                if (!string.IsNullOrEmpty(email))
                {
                    var nguoiDung = await context.NguoiDungs
                        .FirstOrDefaultAsync(n => n.Email == email);
                    
                    if (nguoiDung != null)
                    {
                        return nguoiDung.MaNguoiDung;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the current user's email from claims
        /// </summary>
        public static string GetUserEmail(this ClaimsPrincipal user)
        {
            if (!user.Identity.IsAuthenticated)
                return string.Empty;

            // Try different claim types for email
            return user.FindFirst(ClaimTypes.Email)?.Value ??
                   user.FindFirst("email")?.Value ??
                   user.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress")?.Value ??
                   string.Empty;
        }

        /// <summary>
        /// Gets the current user's full name from claims
        /// </summary>
        public static string GetUserFullName(this ClaimsPrincipal user)
        {
            if (!user.Identity.IsAuthenticated)
                return "Khách";

            return user.FindFirst("FullName")?.Value ??
                   user.FindFirst(ClaimTypes.Name)?.Value ??
                   user.FindFirst("HoTen")?.Value ??
                   user.Identity.Name ??
                   "Người dùng";
        }
    }
}
